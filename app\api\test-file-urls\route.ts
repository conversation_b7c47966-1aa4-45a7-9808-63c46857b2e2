import { NextResponse } from 'next/server';
import { getFileUrl, fileAccessManager } from '@/lib/file-config';

export async function GET() {
  try {
    // Test file URL generation
    const testUrls = [
      '/uploads/courses/test-image.jpg',
      'uploads/courses/test-image.jpg',
      'courses/test-image.jpg',
      'https://ik.imagekit.io/1nqrnok7v/course-gallery/test.jpg'
    ];

    const results = testUrls.map(url => ({
      original: url,
      converted: getFileUrl(url),
      config: fileAccessManager.getConfig()
    }));

    return NextResponse.json({
      message: 'File URL test results',
      results,
      dashboardUrl: process.env.NEXT_PUBLIC_DASHBOARD_URL
    });
  } catch (error) {
    console.error('Test error:', error);
    return NextResponse.json(
      { error: 'Test failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
