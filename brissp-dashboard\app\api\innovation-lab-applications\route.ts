/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    let query = `
      SELECT 
        application_id, applicant_name, email, phone, university_organization,
        project_title, innovation_type, development_stage, status, application_date
      FROM innovation_lab_applications
    `;
    
    const queryParams: any[] = [];

    if (status) {
      query += ' WHERE status = ?';
      queryParams.push(status);
    }

    query += ' ORDER BY application_date DESC LIMIT ? OFFSET ?';
    queryParams.push(limit, offset);

    const [applications] = await pool.query(query, queryParams);

    return NextResponse.json({
      applications,
      pagination: {
        limit,
        offset,
        total: (applications as any[]).length
      }
    });

  } catch (error) {
    console.error('Error fetching innovation lab applications:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching applications' },
      { status: 500 }
    );
  }
}
