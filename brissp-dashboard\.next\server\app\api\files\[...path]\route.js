const CHUNK_PUBLIC_PATH = "server/app/api/files/[...path]/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_fab26e._.js");
runtime.loadChunk("server/chunks/[root of the server]__4273ea._.js");
runtime.loadChunk("server/chunks/_60e788._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/files/[...path]/route/actions.js [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/api/files/[...path]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
