{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport function formatBytes(bytes: number, decimals = 2) {\r\n  if (bytes === 0) return '0 Bytes';\r\n  \r\n  const k = 1024;\r\n  const dm = decimals < 0 ? 0 : decimals;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];\r\n  \r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  \r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\r\n}\r\n\r\n/**\r\n * Generates a random password with specified length\r\n * @param length Length of the password (default: 10)\r\n * @returns Random password string\r\n */\r\nexport function generateRandomPassword(length: number = 10): string {\r\n  const charset = \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*\";\r\n  let password = \"\";\r\n  \r\n  for (let i = 0; i < length; i++) {\r\n    const randomIndex = Math.floor(Math.random() * charset.length);\r\n    password += charset[randomIndex];\r\n  }\r\n  \r\n  return password;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa,EAAE,WAAW,CAAC;IACrD,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,KAAK,WAAW,IAAI,IAAI;IAC9B,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvE,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,MAAM,KAAK,CAAC,EAAE;AAC1E;AAOO,SAAS,uBAAuB,SAAiB,EAAE;IACxD,MAAM,UAAU;IAChB,IAAI,WAAW;IAEf,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM;QAC7D,YAAY,OAAO,CAAC,YAAY;IAClC;IAEA,OAAO;AACT"}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,sMAAM,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,sMAAM,UAAU,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/CourseForm.tsx"], "sourcesContent": ["/* eslint-disable react/no-unescaped-entities */\r\n\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Course } from \"@/lib/types\";\r\nimport { useEditor, EditorContent } from \"@tiptap/react\";\r\nimport StarterKit from \"@tiptap/starter-kit\";\r\n\r\ninterface CourseFormProps {\r\n  initialData?: Course;\r\n  onSubmit: (data: Partial<Course>, imageFile?: File) => void;\r\n  isLoading: boolean;\r\n}\r\n\r\nexport default function CourseForm({\r\n  initialData,\r\n  onSubmit,\r\n  isLoading,\r\n}: CourseFormProps) {\r\n  const [formData, setFormData] = useState<Partial<Course>>(\r\n    initialData || {\r\n      title: \"\",\r\n      course_code: \"\",\r\n      description: \"\",\r\n      duration_months: 0,\r\n      price: 0,\r\n      department: \"\",\r\n      category: \"adults\",\r\n      program_type: \"\",\r\n      num_lectures: 0,\r\n      skill_level: \"beginner\",\r\n      languages: \"\",\r\n      class_days: \"\",\r\n    }\r\n  );\r\n\r\n  const [imageFile, setImageFile] = useState<File | null>(null);\r\n\r\n  const editor = useEditor({\r\n    extensions: [StarterKit],\r\n    content: formData.description,\r\n    onUpdate: ({ editor }) => {\r\n      setFormData((prev) => ({\r\n        ...prev,\r\n        description: editor.getHTML(),\r\n      }));\r\n    },\r\n  });\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    onSubmit(formData, imageFile || undefined);\r\n  };\r\n\r\n  const handleChange = (\r\n    e: React.ChangeEvent<\r\n      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement\r\n    >\r\n  ) => {\r\n    const { name, value } = e.target;\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      [name]: value,\r\n    }));\r\n  };\r\n\r\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = e.target.files?.[0] || null;\r\n    setImageFile(file);\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n      <div className=\"grid grid-cols-2 gap-6\">\r\n        <div>\r\n          <label className=\"block text-sm font-medium mb-2\">Course Title</label>\r\n          <Input\r\n            name=\"title\"\r\n            value={formData.title}\r\n            onChange={handleChange}\r\n            required\r\n          />\r\n        </div>\r\n        <div>\r\n          <label className=\"block text-sm font-medium mb-2\">Course Code</label>\r\n          <Input\r\n            name=\"course_code\"\r\n            value={formData.course_code}\r\n            onChange={handleChange}\r\n            required\r\n          />\r\n        </div>\r\n        <div className=\"col-span-2\">\r\n          <label className=\"block text-sm font-medium mb-2\">Description</label>\r\n          <div className=\"border rounded-md p-2\">\r\n            <div className=\"border-b p-2 mb-2 flex gap-2\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => editor?.chain().focus().toggleBold().run()}\r\n                className={`p-2 ${\r\n                  editor?.isActive(\"bold\") ? \"bg-gray-200\" : \"\"\r\n                }`}\r\n              >\r\n                Bold\r\n              </button>\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => editor?.chain().focus().toggleItalic().run()}\r\n                className={`p-2 ${\r\n                  editor?.isActive(\"italic\") ? \"bg-gray-200\" : \"\"\r\n                }`}\r\n              >\r\n                Italic\r\n              </button>\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => editor?.chain().focus().toggleBulletList().run()}\r\n                className={`p-2 ${\r\n                  editor?.isActive(\"bulletList\") ? \"bg-gray-200\" : \"\"\r\n                }`}\r\n              >\r\n                Bullet List\r\n              </button>\r\n            </div>\r\n            <EditorContent\r\n              editor={editor}\r\n              className=\"min-h-[200px] prose max-w-none\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <div>\r\n          <label className=\"block text-sm font-medium mb-2\">\r\n            Duration (months)\r\n          </label>\r\n          <Input\r\n            type=\"number\"\r\n            name=\"duration_months\"\r\n            value={formData.duration_months}\r\n            onChange={handleChange}\r\n            required\r\n          />\r\n        </div>\r\n        <div>\r\n          <label className=\"block text-sm font-medium mb-2\">Price</label>\r\n          <Input\r\n            type=\"number\"\r\n            name=\"price\"\r\n            value={formData.price}\r\n            onChange={handleChange}\r\n            required\r\n          />\r\n        </div>\r\n        <div>\r\n          <label className=\"block text-sm font-medium mb-2\">Department</label>\r\n          <select    \r\n           title=\"department\"\r\n            name=\"department\"\r\n            value={formData.department}\r\n            onChange={handleChange}\r\n            className=\"w-full border rounded-md p-2\"\r\n            required\r\n          >\r\n            <option value=\"beginner\">Auto Engineering</option>\r\n            <option value=\"intermediate\">Computer Science</option>\r\n          </select>\r\n        </div>\r\n        <div>\r\n          <label className=\"block text-sm font-medium mb-2\">Category</label>\r\n          <select\r\n            title=\"category\"\r\n            name=\"category\"\r\n            value={formData.category}\r\n            onChange={handleChange}\r\n            className=\"w-full border rounded-md p-2\"\r\n            required\r\n          >\r\n            <option value=\"adults\">Adults</option>\r\n            <option value=\"kids\">Kids</option>\r\n          </select>\r\n        </div>\r\n        <div>\r\n          <label className=\"block text-sm font-medium mb-2\">Program Type</label>\r\n          <select\r\n            title=\"program_type\"\r\n            name=\"program_type\"\r\n            value={formData.program_type}\r\n            onChange={handleChange}\r\n            className=\"w-full border rounded-md p-2\"\r\n            required\r\n          >\r\n            <option value=\"\">Select program type</option>\r\n            <option value=\"fulltime\">Fulltime</option>\r\n            <option value=\"adults-online\">Adults online</option>\r\n            <option value=\"kids-online\">Kids online</option>\r\n            <option value=\"distance\">Distance</option>\r\n            <option value=\"part-time\">Part time</option>\r\n            <option value=\"weekend\">Weekend</option>\r\n          </select>\r\n          <p className=\"text-xs text-gray-500 mt-1\">\r\n            For refresher courses, use the dedicated \"Add Refresher Course\" button\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <label className=\"block text-sm font-medium mb-2\">\r\n            Number of Lectures\r\n          </label>\r\n          <Input\r\n            type=\"number\"\r\n            name=\"num_lectures\"\r\n            value={formData.num_lectures}\r\n            onChange={handleChange}\r\n            required\r\n          />\r\n        </div>\r\n        <div>\r\n          <label className=\"block text-sm font-medium mb-2\">Skill Level</label>\r\n          <select\r\n            title=\"skill_level\"\r\n            name=\"skill_level\"\r\n            value={formData.skill_level}\r\n            onChange={handleChange}\r\n            className=\"w-full border rounded-md p-2\"\r\n            required\r\n          >\r\n            <option value=\"all\">All</option>\r\n            <option value=\"beginner\">Beginner</option>\r\n            <option value=\"intermediate\">Intermediate</option>\r\n            <option value=\"advanced\">Advanced</option>\r\n          </select>\r\n        </div>\r\n        <div>\r\n          <label className=\"block text-sm font-medium mb-2\">Languages</label>\r\n          <Input\r\n            name=\"languages\"\r\n            value={formData.languages}\r\n            onChange={handleChange}\r\n            required\r\n          />\r\n        </div>\r\n        <div>\r\n          <label className=\"block text-sm font-medium mb-2\">Class Days</label>\r\n          <Input\r\n            name=\"class_days\"\r\n            value={formData.class_days}\r\n            onChange={handleChange}\r\n            required\r\n          />\r\n        </div>\r\n        <div>\r\n          <label className=\"block text-sm font-medium mb-2\">Upload Image</label>\r\n          <Input type=\"file\" accept=\"image/*\" onChange={handleFileChange} />\r\n        </div>\r\n      </div>\r\n      <div className=\"flex justify-end space-x-4\">\r\n        <Button type=\"submit\" disabled={isLoading}>\r\n          {isLoading ? \"Saving...\" : \"Save Course\"}\r\n        </Button>\r\n      </div>\r\n    </form>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAG9C;AACA;AACA;AAGA;AADA;AANA;;;;;;;AAee,SAAS,WAAW,EACjC,WAAW,EACX,QAAQ,EACR,SAAS,EACO;IAChB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACrC,eAAe;QACb,OAAO;QACP,aAAa;QACb,aAAa;QACb,iBAAiB;QACjB,OAAO;QACP,YAAY;QACZ,UAAU;QACV,cAAc;QACd,cAAc;QACd,aAAa;QACb,WAAW;QACX,YAAY;IACd;IAGF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAExD,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACvB,YAAY;YAAC,2JAAA,CAAA,UAAU;SAAC;QACxB,SAAS,SAAS,WAAW;QAC7B,UAAU,CAAC,EAAE,MAAM,EAAE;YACnB,YAAY,CAAC,OAAS,CAAC;oBACrB,GAAG,IAAI;oBACP,aAAa,OAAO,OAAO;gBAC7B,CAAC;QACH;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,SAAS,UAAU,aAAa;IAClC;IAEA,MAAM,eAAe,CACnB;QAIA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;QACpC,aAAa;IACf;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAClD,8OAAC,0HAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,QAAQ;;;;;;;;;;;;kCAGZ,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAClD,8OAAC,0HAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,OAAO,SAAS,WAAW;gCAC3B,UAAU;gCACV,QAAQ;;;;;;;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAClD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,QAAQ,QAAQ,QAAQ,aAAa;gDACpD,WAAW,CAAC,IAAI,EACd,QAAQ,SAAS,UAAU,gBAAgB,IAC3C;0DACH;;;;;;0DAGD,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,QAAQ,QAAQ,QAAQ,eAAe;gDACtD,WAAW,CAAC,IAAI,EACd,QAAQ,SAAS,YAAY,gBAAgB,IAC7C;0DACH;;;;;;0DAGD,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,QAAQ,QAAQ,QAAQ,mBAAmB;gDAC1D,WAAW,CAAC,IAAI,EACd,QAAQ,SAAS,gBAAgB,gBAAgB,IACjD;0DACH;;;;;;;;;;;;kDAIH,8OAAC,kKAAA,CAAA,gBAAa;wCACZ,QAAQ;wCACR,WAAU;;;;;;;;;;;;;;;;;;kCAIhB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAGlD,8OAAC,0HAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,MAAK;gCACL,OAAO,SAAS,eAAe;gCAC/B,UAAU;gCACV,QAAQ;;;;;;;;;;;;kCAGZ,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAClD,8OAAC,0HAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,QAAQ;;;;;;;;;;;;kCAGZ,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAClD,8OAAC;gCACA,OAAM;gCACL,MAAK;gCACL,OAAO,SAAS,UAAU;gCAC1B,UAAU;gCACV,WAAU;gCACV,QAAQ;;kDAER,8OAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,8OAAC;wCAAO,OAAM;kDAAe;;;;;;;;;;;;;;;;;;kCAGjC,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAClD,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,OAAO,SAAS,QAAQ;gCACxB,UAAU;gCACV,WAAU;gCACV,QAAQ;;kDAER,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;;;;;;;;;;;;;kCAGzB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAClD,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,OAAO,SAAS,YAAY;gCAC5B,UAAU;gCACV,WAAU;gCACV,QAAQ;;kDAER,8OAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,8OAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,8OAAC;wCAAO,OAAM;kDAAgB;;;;;;kDAC9B,8OAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,8OAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,8OAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,8OAAC;wCAAO,OAAM;kDAAU;;;;;;;;;;;;0CAE1B,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAI5C,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAGlD,8OAAC,0HAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,MAAK;gCACL,OAAO,SAAS,YAAY;gCAC5B,UAAU;gCACV,QAAQ;;;;;;;;;;;;kCAGZ,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAClD,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,OAAO,SAAS,WAAW;gCAC3B,UAAU;gCACV,WAAU;gCACV,QAAQ;;kDAER,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,8OAAC;wCAAO,OAAM;kDAAe;;;;;;kDAC7B,8OAAC;wCAAO,OAAM;kDAAW;;;;;;;;;;;;;;;;;;kCAG7B,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAClD,8OAAC,0HAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,OAAO,SAAS,SAAS;gCACzB,UAAU;gCACV,QAAQ;;;;;;;;;;;;kCAGZ,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAClD,8OAAC,0HAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,OAAO,SAAS,UAAU;gCAC1B,UAAU;gCACV,QAAQ;;;;;;;;;;;;kCAGZ,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAClD,8OAAC,0HAAA,CAAA,QAAK;gCAAC,MAAK;gCAAO,QAAO;gCAAU,UAAU;;;;;;;;;;;;;;;;;;0BAGlD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;oBAAC,MAAK;oBAAS,UAAU;8BAC7B,YAAY,cAAc;;;;;;;;;;;;;;;;;AAKrC"}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/app/%28dashboard%29/courses/new/page.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState } from \"react\"\r\nimport { useRouter } from \"next/navigation\"\r\nimport CourseForm from \"@/components/CourseForm\"\r\nimport { Course } from \"@/lib/types\"\r\n\r\nexport default function NewCoursePage() {\r\n  const router = useRouter()\r\n  const [isLoading, setIsLoading] = useState(false)\r\n\r\n  const handleSubmit = async (data: Partial<Course>, imageFile?: File) => {\r\n    setIsLoading(true)\r\n    try {\r\n      let imageUrl = data.image_url\r\n\r\n      // Handle image upload if a new file is provided\r\n      if (imageFile) {\r\n        const formData = new FormData()\r\n        formData.append('file', imageFile)\r\n\r\n        const uploadResponse = await fetch('/api/upload', {\r\n          method: 'POST',\r\n          body: formData,\r\n        })\r\n\r\n        if (uploadResponse.ok) {\r\n          const { url } = await uploadResponse.json()\r\n          imageUrl = url\r\n        }\r\n      }\r\n\r\n      // Create course with image URL\r\n      const response = await fetch('/api/courses', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          ...data,\r\n          image_url: imageUrl,\r\n        }),\r\n      })\r\n      \r\n      if (response.ok) {\r\n        router.push('/courses')\r\n      }\r\n    } catch (error) {\r\n      console.error('Error creating course:', error)\r\n    } finally {\r\n      setIsLoading(false)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"p-6 \">\r\n      <h1 className=\"text-2xl font-bold mb-6\">Create New Course</h1>\r\n      <div className=\"bg-white rounded-lg border p-6\">\r\n        <CourseForm onSubmit={handleSubmit} isLoading={isLoading} />\r\n      </div>\r\n    </div>\r\n  )\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAOe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,OAAO,MAAuB;QACjD,aAAa;QACb,IAAI;YACF,IAAI,WAAW,KAAK,SAAS;YAE7B,gDAAgD;YAChD,IAAI,WAAW;gBACb,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,QAAQ;gBAExB,MAAM,iBAAiB,MAAM,MAAM,eAAe;oBAChD,QAAQ;oBACR,MAAM;gBACR;gBAEA,IAAI,eAAe,EAAE,EAAE;oBACrB,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,eAAe,IAAI;oBACzC,WAAW;gBACb;YACF;YAEA,+BAA+B;YAC/B,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,IAAI;oBACP,WAAW;gBACb;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,yHAAA,CAAA,UAAU;oBAAC,UAAU;oBAAc,WAAW;;;;;;;;;;;;;;;;;AAIvD"}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}