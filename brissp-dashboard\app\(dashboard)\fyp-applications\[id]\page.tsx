/* eslint-disable react-hooks/exhaustive-deps */
"use client"

import { useState, useEffect } from "react"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, User, GraduationCap, FileText, Calendar, Settings, Save } from "lucide-react"
import { FYPApplication } from "@/app/api/fyp-applications/route"

export default function FYPApplicationDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const [application, setApplication] = useState<FYPApplication | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [status, setStatus] = useState("")
  const [reviewNotes, setReviewNotes] = useState("")
  const [assignedSupervisor, setAssignedSupervisor] = useState("")

  useEffect(() => {
    if (params?.id) {
      fetchApplication()
    }
  }, [params?.id])

  const fetchApplication = async () => {
    try {
      const response = await fetch(`/api/fyp-applications/${params?.id}`)
      const data = await response.json()

      if (response.ok) {
        setApplication(data.application)
        setStatus(data.application.status)
        setReviewNotes(data.application.review_notes || "")
        setAssignedSupervisor(data.application.assigned_supervisor || "")
      }
    } catch (error) {
      console.error('Error fetching application:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleUpdate = async () => {
    if (!application) return

    setUpdating(true)
    try {
      const response = await fetch(`/api/fyp-applications/${params?.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status,
          review_notes: reviewNotes,
          assigned_supervisor: assignedSupervisor
        }),
      })

      if (response.ok) {
        await fetchApplication()
        alert('Application updated successfully!')
      }
    } catch (error) {
      console.error('Error updating application:', error)
      alert('Error updating application')
    } finally {
      setUpdating(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: "secondary" as const, label: "Pending" },
      reviewed: { variant: "outline" as const, label: "Reviewed" },
      accepted: { variant: "default" as const, label: "Accepted" },
      rejected: { variant: "destructive" as const, label: "Rejected" },
      "in-progress": { variant: "default" as const, label: "In Progress" },
      completed: { variant: "default" as const, label: "Completed" }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center">Loading application details...</div>
      </div>
    )
  }

  if (!application) {
    return (
      <div className="p-6">
        <div className="text-center text-red-600">Application not found</div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Final Year Project Application</h1>
            <p className="text-gray-600">Application ID: {application.application_id}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {getStatusBadge(application.status)}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Student Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="w-5 h-5" />
                <span>Student Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Full Name</label>
                  <p className="font-medium">{application.student_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <p className="font-medium">{application.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Phone</label>
                  <p className="font-medium">{application.phone}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Student ID</label>
                  <p className="font-medium">{application.student_id || 'Not provided'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Academic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <GraduationCap className="w-5 h-5" />
                <span>Academic Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">University</label>
                  <p className="font-medium">{application.university}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Course of Study</label>
                  <p className="font-medium">{application.course_of_study}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Year of Study</label>
                  <p className="font-medium">{application.year_of_study}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Project Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="w-5 h-5" />
                <span>Project Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Project Title</label>
                <p className="font-medium">{application.project_title || 'Not provided'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Project Description</label>
                <p className="text-sm">{application.project_description || 'Not provided'}</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Project Type</label>
                  <p className="font-medium capitalize">{application.project_type}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Research Area</label>
                  <p className="font-medium">{application.research_area || 'Not specified'}</p>
                </div>
              </div>
              {application.methodology && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Methodology</label>
                  <p className="text-sm">{application.methodology}</p>
                </div>
              )}
              {application.expected_outcomes && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Expected Outcomes</label>
                  <p className="text-sm">{application.expected_outcomes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Timeline and Resources */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="w-5 h-5" />
                <span>Timeline & Resources</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Timeline (Weeks)</label>
                  <p className="font-medium">{application.timeline_weeks || 'Not specified'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Project Deadline</label>
                  <p className="font-medium">
                    {application.project_deadline 
                      ? new Date(application.project_deadline).toLocaleDateString()
                      : 'Not specified'
                    }
                  </p>
                </div>
              </div>
              {application.required_resources && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Required Resources</label>
                  <p className="text-sm">{application.required_resources}</p>
                </div>
              )}
              {application.technical_requirements && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Technical Requirements</label>
                  <p className="text-sm">{application.technical_requirements}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Application Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="w-5 h-5" />
                <span>Application Management</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">Status</label>
                <Select value={status} onValueChange={setStatus}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="reviewed">Reviewed</SelectItem>
                    <SelectItem value="accepted">Accepted</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                    <SelectItem value="in-progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Assigned Supervisor</label>
                <input
                  type="text"
                  value={assignedSupervisor}
                  onChange={(e) => setAssignedSupervisor(e.target.value)}
                  className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Enter supervisor name"
                />
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Review Notes</label>
                <Textarea
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  placeholder="Add review notes..."
                  rows={4}
                />
              </div>

              <Button onClick={handleUpdate} disabled={updating} className="w-full">
                <Save className="w-4 h-4 mr-2" />
                {updating ? 'Updating...' : 'Update Application'}
              </Button>
            </CardContent>
          </Card>

          {/* Application Details */}
          <Card>
            <CardHeader>
              <CardTitle>Application Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <label className="text-sm font-medium text-gray-500">Applied Date</label>
                <p className="text-sm">{new Date(application.application_date).toLocaleDateString()}</p>
              </div>
              {application.review_date && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Review Date</label>
                  <p className="text-sm">{new Date(application.review_date).toLocaleDateString()}</p>
                </div>
              )}
              {application.supervisor_name && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Current Supervisor</label>
                  <p className="text-sm">{application.supervisor_name}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
