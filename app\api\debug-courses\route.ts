import { NextResponse } from 'next/server';
import pool from '@/lib/db';
import { getFileUrl } from '@/lib/file-config';

export async function GET() {
  try {
    // Get a few courses to see their image URLs
    const [courses] = await pool.query('SELECT course_id, title, image_url FROM courses LIMIT 5');
    
    const debugInfo = (courses as any[]).map(course => ({
      course_id: course.course_id,
      title: course.title,
      original_image_url: course.image_url,
      converted_image_url: course.image_url ? getFileUrl(course.image_url) : null,
      dashboard_url: process.env.NEXT_PUBLIC_DASHBOARD_URL
    }));

    return NextResponse.json({
      message: 'Course image URL debug info',
      courses: debugInfo,
      environment: {
        dashboard_url: process.env.NEXT_PUBLIC_DASHBOARD_URL,
        node_env: process.env.NODE_ENV
      }
    });
  } catch (error) {
    console.error('Debug error:', error);
    return NextResponse.json(
      { error: 'Debug failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
