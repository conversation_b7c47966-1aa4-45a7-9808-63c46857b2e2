{"name": "academy", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "migrate-files": "tsx scripts/migrate-files.ts"}, "dependencies": {"@clerk/nextjs": "^6.10.3", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/nodemailer": "^6.4.17", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "cloudinary": "^2.5.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "dotenv": "^17.2.0", "imagekit": "^6.0.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.474.0", "mysql2": "^3.12.0", "next": "^15.1.6", "nodemailer": "^6.10.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "resend": "^4.1.2", "sonner": "^2.0.1", "svix": "^1.61.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "aws-sdk": "^2.1692.0", "eslint": "^9", "eslint-config-next": "15.1.6", "html-loader": "^5.1.0", "mock-aws-s3": "^4.0.2", "nock": "^14.0.1", "node-loader": "^2.1.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}