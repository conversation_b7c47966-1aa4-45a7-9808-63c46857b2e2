import { NextRequest, NextResponse } from 'next/server';
import { fileManager } from '@/lib/file-management';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'stats':
        const stats = await fileManager.getStorageStats();
        return NextResponse.json(stats);

      case 'orphaned':
        const orphanedFiles = await fileManager.findOrphanedFiles();
        return NextResponse.json(orphanedFiles);

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: stats, orphaned' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('File management error:', error);
    return NextResponse.json(
      { error: 'Failed to perform file management operation' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action, options } = await request.json();

    switch (action) {
      case 'cleanup':
        const olderThanDays = options?.olderThanDays || 7;
        const cleanupResult = await fileManager.cleanupOrphanedFiles(olderThanDays);
        return NextResponse.json(cleanupResult);

      case 'migrate':
        const migrationResult = await fileManager.migrateFileUrls();
        return NextResponse.json(migrationResult);

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: cleanup, migrate' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('File management error:', error);
    return NextResponse.json(
      { error: 'Failed to perform file management operation' },
      { status: 500 }
    );
  }
}
