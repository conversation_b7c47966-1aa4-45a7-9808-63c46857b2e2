{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport function formatBytes(bytes: number, decimals = 2) {\r\n  if (bytes === 0) return '0 Bytes';\r\n  \r\n  const k = 1024;\r\n  const dm = decimals < 0 ? 0 : decimals;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];\r\n  \r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  \r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\r\n}\r\n\r\n/**\r\n * Generates a random password with specified length\r\n * @param length Length of the password (default: 10)\r\n * @returns Random password string\r\n */\r\nexport function generateRandomPassword(length: number = 10): string {\r\n  const charset = \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*\";\r\n  let password = \"\";\r\n  \r\n  for (let i = 0; i < length; i++) {\r\n    const randomIndex = Math.floor(Math.random() * charset.length);\r\n    password += charset[randomIndex];\r\n  }\r\n  \r\n  return password;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa,EAAE,WAAW,CAAC;IACrD,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,KAAK,WAAW,IAAI,IAAI;IAC9B,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvE,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,MAAM,KAAK,CAAC,EAAE;AAC1E;AAOO,SAAS,uBAAuB,SAAiB,EAAE;IACxD,MAAM,UAAU;IAChB,IAAI,WAAW;IAEf,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM;QAC7D,YAAY,OAAO,CAAC,YAAY;IAClC;IAEA,OAAO;AACT"}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,8JAAM,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      ref={ref}\r\n      className={cn(\"w-full caption-bottom text-sm\", className)}\r\n      {...props}\r\n    />\r\n  </div>\r\n))\r\nTable.displayName = \"Table\"\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\r\n))\r\nTableHeader.displayName = \"TableHeader\"\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    ref={ref}\r\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableBody.displayName = \"TableBody\"\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableFooter.displayName = \"TableFooter\"\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableRow.displayName = \"TableRow\"\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    ref={ref}\r\n    className={cn(\r\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableHead.displayName = \"TableHead\"\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    ref={ref}\r\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCell.displayName = \"TableCell\"\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    ref={ref}\r\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCaption.displayName = \"TableCaption\"\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,8JAAM,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,8JAAM,UAAU,OAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,8JAAM,UAAU,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,8JAAM,UAAU,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,8JAAM,UAAU,QAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,aAAa,WAAW,GAAG"}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS"}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/app/%28dashboard%29/innovation-lab-applications/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Eye, Trash2, Cpu, Clock, CheckCircle, XCircle, FileText } from \"lucide-react\";\nimport Link from \"next/link\";\n\ninterface InnovationLabApplication {\n  application_id: number;\n  applicant_name: string;\n  email: string;\n  phone: string;\n  university_organization: string;\n  project_title: string;\n  innovation_type: string;\n  development_stage: string;\n  status: 'pending' | 'reviewed' | 'accepted' | 'rejected' | 'in-progress' | 'completed';\n  application_date: string;\n}\n \nexport default function InnovationLabApplicationsPage() {\n  const [applications, setApplications] = useState<InnovationLabApplication[]>([]);\n  const [filteredApplications, setFilteredApplications] = useState<InnovationLabApplication[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'reviewed' | 'accepted' | 'in-progress' | 'completed'>('all');\n\n  useEffect(() => {\n    fetchApplications();\n  }, []);\n\n  useEffect(() => {\n    const filterApplications = () => {\n      switch (activeTab) {\n        case 'pending':\n          setFilteredApplications(applications.filter(app => app.status === 'pending'));\n          break;\n        case 'reviewed':\n          setFilteredApplications(applications.filter(app => app.status === 'reviewed'));\n          break;\n        case 'accepted':\n          setFilteredApplications(applications.filter(app => app.status === 'accepted'));\n          break;\n        case 'in-progress':\n          setFilteredApplications(applications.filter(app => app.status === 'in-progress'));\n          break;\n        case 'completed':\n          setFilteredApplications(applications.filter(app => app.status === 'completed'));\n          break;\n        default:\n          setFilteredApplications(applications);\n      }\n    };\n    \n    filterApplications();\n  }, [applications, activeTab]);\n\n  const fetchApplications = async () => {\n    try {\n      const response = await fetch(\"/api/innovation-lab-applications\");\n      const data = await response.json();\n      setApplications(data.applications || []);\n    } catch (error) {\n      console.error(\"Error fetching innovation lab applications:\", error);\n    }\n  };\n\n  const handleDelete = async (applicationId: number) => {\n    if (confirm(\"Are you sure you want to delete this application?\")) {\n      setIsLoading(true);\n      try {\n        const response = await fetch(`/api/innovation-lab-applications/${applicationId}`, {\n          method: \"DELETE\",\n        });\n        if (response.ok) {\n          fetchApplications();\n        }\n      } catch (error) {\n        console.error(\"Error deleting innovation lab application:\", error);\n      } finally {\n        setIsLoading(false);\n      }\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      pending: { variant: \"secondary\" as const, label: \"Pending\", className: \"bg-yellow-100 text-yellow-800\" },\n      reviewed: { variant: \"outline\" as const, label: \"Reviewed\", className: \"bg-blue-100 text-blue-800\" },\n      accepted: { variant: \"default\" as const, label: \"Accepted\", className: \"bg-green-100 text-green-800\" },\n      rejected: { variant: \"destructive\" as const, label: \"Rejected\", className: \"bg-red-100 text-red-800\" },\n      \"in-progress\": { variant: \"default\" as const, label: \"In Progress\", className: \"bg-purple-100 text-purple-800\" },\n      completed: { variant: \"default\" as const, label: \"Completed\", className: \"bg-emerald-100 text-emerald-800\" },\n    };\n    \n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;\n    return (\n      <Badge className={config.className}>\n        {config.label}\n      </Badge>\n    );\n  };\n\n  const getInnovationTypeBadge = (type: string) => {\n    const typeColors = {\n      \"software\": \"bg-blue-100 text-blue-800\",\n      \"hardware\": \"bg-orange-100 text-orange-800\",\n      \"ai-ml\": \"bg-purple-100 text-purple-800\",\n      \"biotech\": \"bg-green-100 text-green-800\",\n      \"fintech\": \"bg-yellow-100 text-yellow-800\",\n      \"edtech\": \"bg-indigo-100 text-indigo-800\",\n      \"cleantech\": \"bg-emerald-100 text-emerald-800\",\n      \"other\": \"bg-gray-100 text-gray-800\"\n    };\n    \n    return (\n      <span className={`px-2 py-1 rounded-full text-xs font-medium ${typeColors[type as keyof typeof typeColors] || 'bg-gray-100 text-gray-800'}`}>\n        {type ? type.charAt(0).toUpperCase() + type.slice(1).replace('-', '/') : 'N/A'}\n      </span>\n    );\n  };\n\n  // Calculate counts for tabs\n  const getApplicationCounts = () => {\n    return {\n      all: applications.length,\n      pending: applications.filter(app => app.status === 'pending').length,\n      reviewed: applications.filter(app => app.status === 'reviewed').length,\n      accepted: applications.filter(app => app.status === 'accepted').length,\n      inProgress: applications.filter(app => app.status === 'in-progress').length,\n      completed: applications.filter(app => app.status === 'completed').length,\n    };\n  };\n\n  const counts = getApplicationCounts();\n\n  const ApplicationTable = ({ applications }: { applications: InnovationLabApplication[] }) => (\n    <Table>\n      <TableHeader>\n        <TableRow>\n          <TableHead>Applicant</TableHead>\n          <TableHead>Project Title</TableHead>\n          <TableHead>Innovation Type</TableHead>\n          <TableHead>Stage</TableHead>\n          <TableHead>Status</TableHead>\n          <TableHead>Applied Date</TableHead>\n          <TableHead>Actions</TableHead>\n        </TableRow>\n      </TableHeader>\n      <TableBody>\n        {applications.map((app) => (\n          <TableRow key={app.application_id}>\n            <TableCell>\n              <div>\n                <div className=\"font-medium\">{app.applicant_name}</div>\n                <div className=\"text-sm text-gray-500\">{app.email}</div>\n              </div>\n            </TableCell>\n            <TableCell>\n              <div>\n                <div className=\"font-medium max-w-xs truncate\" title={app.project_title}>\n                  {app.project_title}\n                </div>\n                <div className=\"text-sm text-gray-500\">{app.university_organization}</div>\n              </div>\n            </TableCell>\n            <TableCell>{getInnovationTypeBadge(app.innovation_type)}</TableCell>\n            <TableCell>\n              <span className=\"capitalize text-sm\">\n                {app.development_stage.replace('-', ' ')}\n              </span>\n            </TableCell>\n            <TableCell>{getStatusBadge(app.status)}</TableCell>\n            <TableCell>{new Date(app.application_date).toLocaleDateString()}</TableCell>\n            <TableCell>\n              <div className=\"flex space-x-2\">\n                <Link href={`/innovation-lab-applications/${app.application_id}`}>\n                  <Button variant=\"outline\" size=\"sm\">\n                    <Eye className=\"w-4 h-4\" />\n                  </Button>\n                </Link>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => handleDelete(app.application_id)}\n                  disabled={isLoading}\n                >\n                  <Trash2 className=\"w-4 h-4\" />\n                </Button>\n              </div>\n            </TableCell>\n          </TableRow>\n        ))}\n      </TableBody>\n    </Table>\n  );\n\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold\">Innovation Lab Applications</h1>\n          <p className=\"text-gray-600\">Manage innovation lab applications and track project progress</p>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-6 gap-6\">\n        <div className=\"bg-white p-6 rounded-lg border shadow-sm\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Applications</p>\n              <p className=\"text-2xl font-bold\">{counts.all}</p>\n            </div>\n            <FileText className=\"h-8 w-8 text-blue-600\" />\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-lg border shadow-sm\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Pending</p>\n              <p className=\"text-2xl font-bold\">{counts.pending}</p>\n            </div>\n            <Clock className=\"h-8 w-8 text-yellow-600\" />\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-lg border shadow-sm\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Reviewed</p>\n              <p className=\"text-2xl font-bold\">{counts.reviewed}</p>\n            </div>\n            <Eye className=\"h-8 w-8 text-blue-600\" />\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-lg border shadow-sm\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Accepted</p>\n              <p className=\"text-2xl font-bold\">{counts.accepted}</p>\n            </div>\n            <CheckCircle className=\"h-8 w-8 text-green-600\" />\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-lg border shadow-sm\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">In Progress</p>\n              <p className=\"text-2xl font-bold\">{counts.inProgress}</p>\n            </div>\n            <Cpu className=\"h-8 w-8 text-purple-600\" />\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-lg border shadow-sm\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Completed</p>\n              <p className=\"text-2xl font-bold\">{counts.completed}</p>\n            </div>\n            <XCircle className=\"h-8 w-8 text-emerald-600\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Tabbed Interface */}\n      <div className=\"bg-white rounded-lg border shadow-sm\">\n        <div className=\"border-b\">\n          <nav className=\"flex space-x-8 px-6\">\n            <button\n              type=\"button\"\n              onClick={() => setActiveTab('all')}\n              className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'all'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              All Applications ({counts.all})\n            </button>\n            <button\n              type=\"button\"\n              onClick={() => setActiveTab('pending')}\n              className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'pending'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              Pending ({counts.pending})\n            </button>\n            <button\n              type=\"button\"\n              onClick={() => setActiveTab('reviewed')}\n              className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'reviewed'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              Reviewed ({counts.reviewed})\n            </button>\n            <button\n              type=\"button\"\n              onClick={() => setActiveTab('accepted')}\n              className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'accepted'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              Accepted ({counts.accepted})\n            </button>\n            <button\n              type=\"button\"\n              onClick={() => setActiveTab('in-progress')}\n              className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'in-progress'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              In Progress ({counts.inProgress})\n            </button>\n            <button\n              type=\"button\"\n              onClick={() => setActiveTab('completed')}\n              className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'completed'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              Completed ({counts.completed})\n            </button>\n          </nav>\n        </div>\n\n        <div className=\"p-6\">\n          {isLoading ? (\n            <div className=\"text-center py-8\">Loading applications...</div>\n          ) : filteredApplications.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <p>No applications found for this status.</p>\n            </div>\n          ) : (\n            <ApplicationTable applications={filteredApplications} />\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;;AAsBe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B,EAAE;IAC/E,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B,EAAE;IAC/F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6E;IAEtH,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mDAAE;YACR;QACF;kDAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mDAAE;YACR,MAAM;8EAAqB;oBACzB,OAAQ;wBACN,KAAK;4BACH,wBAAwB,aAAa,MAAM;8FAAC,CAAA,MAAO,IAAI,MAAM,KAAK;;4BAClE;wBACF,KAAK;4BACH,wBAAwB,aAAa,MAAM;8FAAC,CAAA,MAAO,IAAI,MAAM,KAAK;;4BAClE;wBACF,KAAK;4BACH,wBAAwB,aAAa,MAAM;8FAAC,CAAA,MAAO,IAAI,MAAM,KAAK;;4BAClE;wBACF,KAAK;4BACH,wBAAwB,aAAa,MAAM;8FAAC,CAAA,MAAO,IAAI,MAAM,KAAK;;4BAClE;wBACF,KAAK;4BACH,wBAAwB,aAAa,MAAM;8FAAC,CAAA,MAAO,IAAI,MAAM,KAAK;;4BAClE;wBACF;4BACE,wBAAwB;oBAC5B;gBACF;;YAEA;QACF;kDAAG;QAAC;QAAc;KAAU;IAE5B,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,gBAAgB,KAAK,YAAY,IAAI,EAAE;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;QAC/D;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,QAAQ,sDAAsD;YAChE,aAAa;YACb,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,iCAAiC,EAAE,eAAe,EAAE;oBAChF,QAAQ;gBACV;gBACA,IAAI,SAAS,EAAE,EAAE;oBACf;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8CAA8C;YAC9D,SAAU;gBACR,aAAa;YACf;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,SAAS;gBAAE,SAAS;gBAAsB,OAAO;gBAAW,WAAW;YAAgC;YACvG,UAAU;gBAAE,SAAS;gBAAoB,OAAO;gBAAY,WAAW;YAA4B;YACnG,UAAU;gBAAE,SAAS;gBAAoB,OAAO;gBAAY,WAAW;YAA8B;YACrG,UAAU;gBAAE,SAAS;gBAAwB,OAAO;gBAAY,WAAW;YAA0B;YACrG,eAAe;gBAAE,SAAS;gBAAoB,OAAO;gBAAe,WAAW;YAAgC;YAC/G,WAAW;gBAAE,SAAS;gBAAoB,OAAO;gBAAa,WAAW;YAAkC;QAC7G;QAEA,MAAM,SAAS,YAAY,CAAC,OAAoC,IAAI,aAAa,OAAO;QACxF,qBACE,6LAAC,6HAAA,CAAA,QAAK;YAAC,WAAW,OAAO,SAAS;sBAC/B,OAAO,KAAK;;;;;;IAGnB;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,aAAa;YACjB,YAAY;YACZ,YAAY;YACZ,SAAS;YACT,WAAW;YACX,WAAW;YACX,UAAU;YACV,aAAa;YACb,SAAS;QACX;QAEA,qBACE,6LAAC;YAAK,WAAW,CAAC,2CAA2C,EAAE,UAAU,CAAC,KAAgC,IAAI,6BAA6B;sBACxI,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,OAAO;;;;;;IAG/E;IAEA,4BAA4B;IAC5B,MAAM,uBAAuB;QAC3B,OAAO;YACL,KAAK,aAAa,MAAM;YACxB,SAAS,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,WAAW,MAAM;YACpE,UAAU,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,YAAY,MAAM;YACtE,UAAU,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,YAAY,MAAM;YACtE,YAAY,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,eAAe,MAAM;YAC3E,WAAW,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,aAAa,MAAM;QAC1E;IACF;IAEA,MAAM,SAAS;IAEf,MAAM,mBAAmB,CAAC,EAAE,YAAY,EAAgD,iBACtF,6LAAC,6HAAA,CAAA,QAAK;;8BACJ,6LAAC,6HAAA,CAAA,cAAW;8BACV,cAAA,6LAAC,6HAAA,CAAA,WAAQ;;0CACP,6LAAC,6HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,6HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,6HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,6HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,6HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,6HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,6HAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;;;;;;;8BAGf,6LAAC,6HAAA,CAAA,YAAS;8BACP,aAAa,GAAG,CAAC,CAAC,oBACjB,6LAAC,6HAAA,CAAA,WAAQ;;8CACP,6LAAC,6HAAA,CAAA,YAAS;8CACR,cAAA,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAe,IAAI,cAAc;;;;;;0DAChD,6LAAC;gDAAI,WAAU;0DAAyB,IAAI,KAAK;;;;;;;;;;;;;;;;;8CAGrD,6LAAC,6HAAA,CAAA,YAAS;8CACR,cAAA,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;gDAAgC,OAAO,IAAI,aAAa;0DACpE,IAAI,aAAa;;;;;;0DAEpB,6LAAC;gDAAI,WAAU;0DAAyB,IAAI,uBAAuB;;;;;;;;;;;;;;;;;8CAGvE,6LAAC,6HAAA,CAAA,YAAS;8CAAE,uBAAuB,IAAI,eAAe;;;;;;8CACtD,6LAAC,6HAAA,CAAA,YAAS;8CACR,cAAA,6LAAC;wCAAK,WAAU;kDACb,IAAI,iBAAiB,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;8CAGxC,6LAAC,6HAAA,CAAA,YAAS;8CAAE,eAAe,IAAI,MAAM;;;;;;8CACrC,6LAAC,6HAAA,CAAA,YAAS;8CAAE,IAAI,KAAK,IAAI,gBAAgB,EAAE,kBAAkB;;;;;;8CAC7D,6LAAC,6HAAA,CAAA,YAAS;8CACR,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,6BAA6B,EAAE,IAAI,cAAc,EAAE;0DAC9D,cAAA,6LAAC,8HAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;8DAC7B,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAGnB,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,aAAa,IAAI,cAAc;gDAC9C,UAAU;0DAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2BApCX,IAAI,cAAc;;;;;;;;;;;;;;;;IA8CzC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAKjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAsB,OAAO,GAAG;;;;;;;;;;;;8CAE/C,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGxB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAsB,OAAO,OAAO;;;;;;;;;;;;8CAEnD,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAsB,OAAO,QAAQ;;;;;;;;;;;;8CAEpD,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGnB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAsB,OAAO,QAAQ;;;;;;;;;;;;8CAEpD,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAsB,OAAO,UAAU;;;;;;;;;;;;8CAEtD,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGnB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAsB,OAAO,SAAS;;;;;;;;;;;;8CAErD,6LAAC,+MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMzB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,QACV,kCACA,8EACJ;;wCACH;wCACoB,OAAO,GAAG;wCAAC;;;;;;;8CAEhC,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,YACV,kCACA,8EACJ;;wCACH;wCACW,OAAO,OAAO;wCAAC;;;;;;;8CAE3B,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,kCACA,8EACJ;;wCACH;wCACY,OAAO,QAAQ;wCAAC;;;;;;;8CAE7B,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,kCACA,8EACJ;;wCACH;wCACY,OAAO,QAAQ;wCAAC;;;;;;;8CAE7B,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,gBACV,kCACA,8EACJ;;wCACH;wCACe,OAAO,UAAU;wCAAC;;;;;;;8CAElC,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,cACV,kCACA,8EACJ;;wCACH;wCACa,OAAO,SAAS;wCAAC;;;;;;;;;;;;;;;;;;kCAKnC,6LAAC;wBAAI,WAAU;kCACZ,0BACC,6LAAC;4BAAI,WAAU;sCAAmB;;;;;mCAChC,qBAAqB,MAAM,KAAK,kBAClC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;0CAAE;;;;;;;;;;iDAGL,6LAAC;4BAAiB,cAAc;;;;;;;;;;;;;;;;;;;;;;;AAM5C;GA3UwB;KAAA"}}, {"offset": {"line": 1203, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}