import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/db';
import { RowDataPacket, ResultSetHeader } from 'mysql2';

export interface PitchDeckApplication extends RowDataPacket {
  application_id: number;
  applicant_name: string;
  email: string;
  phone: string;
  company_name?: string;
  industry?: string;
  funding_stage: 'pre-seed' | 'seed' | 'series-a' | 'series-b' | 'later-stage';
  funding_amount?: string;
  business_description: string;
  target_audience?: string;
  current_traction?: string;
  team_size?: number;
  previous_funding: boolean;
  pitch_deadline?: Date;
  specific_requirements?: string;
  preferred_start_date?: Date;
  budget_range: 'basic' | 'standard' | 'premium' | 'enterprise';
  referral_source?: string;
  status: 'pending' | 'reviewed' | 'accepted' | 'rejected' | 'in-progress' | 'completed';
  application_date: Date;
  reviewed_by?: number;
  review_date?: Date;
  review_notes?: string;
  assigned_consultant?: string;
  project_start_date?: Date;
  project_completion_date?: Date;
  final_pitch_deck_url?: string;
  success_metrics?: string;
  created_at: Date;
  updated_at: Date;
}

// GET all pitch deck applications
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    let query = `
      SELECT * FROM pitch_deck_applications
    `;
    const params: (string | number | null)[] = [];

    if (status) {
      query += ' WHERE status = ?';
      params.push(status);
    }

    query += ' ORDER BY application_date DESC LIMIT ? OFFSET ?';
    params.push(limit, offset);

    const [rows] = await pool.query<PitchDeckApplication[]>(query, params);

    // Get total count for pagination
    let countQuery = 'SELECT COUNT(*) as total FROM pitch_deck_applications';
    const countParams: (string | number | null)[] = [];
    
    if (status) {
      countQuery += ' WHERE status = ?';
      countParams.push(status);
    }

    const [countResult] = await pool.query<RowDataPacket[]>(countQuery, countParams);
    const total = countResult[0].total;

    return NextResponse.json({
      applications: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching pitch deck applications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch applications' },
      { status: 500 }
    );
  }
}

// POST create new pitch deck application
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    const {
      applicant_name,
      email,
      phone,
      company_name,
      industry,
      funding_stage,
      funding_amount,
      business_description,
      target_audience,
      current_traction,
      team_size,
      previous_funding,
      pitch_deadline,
      specific_requirements,
      preferred_start_date,
      budget_range,
      referral_source
    } = data;

    // Validate required fields
    if (!applicant_name || !email || !phone || !funding_stage || !business_description || !budget_range) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const [result] = await pool.query<ResultSetHeader>(
      `INSERT INTO pitch_deck_applications (
        applicant_name, email, phone, company_name, industry, funding_stage,
        funding_amount, business_description, target_audience, current_traction,
        team_size, previous_funding, pitch_deadline, specific_requirements,
        preferred_start_date, budget_range, referral_source
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        applicant_name, email, phone, company_name, industry, funding_stage,
        funding_amount, business_description, target_audience, current_traction,
        team_size, previous_funding, pitch_deadline, specific_requirements,
        preferred_start_date, budget_range, referral_source
      ]
    );

    return NextResponse.json(
      { 
        message: 'Application submitted successfully',
        application_id: result.insertId
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating pitch deck application:', error);
    return NextResponse.json(
      { error: 'Failed to submit application' },
      { status: 500 }
    );
  }
}

// PUT update application status
export async function PUT(request: NextRequest) {
  try {
    const data = await request.json();
    const { application_id, status, review_notes, assigned_consultant } = data;

    if (!application_id || !status) {
      return NextResponse.json(
        { error: 'Application ID and status are required' },
        { status: 400 }
      );
    }

    const [result] = await pool.query<ResultSetHeader>(
      `UPDATE pitch_deck_applications 
       SET status = ?, review_notes = ?, assigned_consultant = ?, review_date = NOW()
       WHERE application_id = ?`,
      [status, review_notes, assigned_consultant, application_id]
    );

    if (result.affectedRows === 0) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Application updated successfully' });
  } catch (error) {
    console.error('Error updating pitch deck application:', error);
    return NextResponse.json(
      { error: 'Failed to update application' },
      { status: 500 }
    );
  }
}
