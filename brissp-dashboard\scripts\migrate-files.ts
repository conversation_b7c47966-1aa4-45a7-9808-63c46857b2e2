#!/usr/bin/env tsx

/**
 * File Migration Script
 * 
 * This script helps migrate from external file services (ImageKit, Cloudinary)
 * to the local file storage system.
 * 
 * Usage:
 * npm run migrate-files [options]
 * 
 * Options:
 * --dry-run: Show what would be migrated without making changes
 * --download: Download external files to local storage
 * --update-db: Update database URLs to point to local files
 * --cleanup: Remove orphaned files after migration
 */

import { fileManager } from '../lib/file-management';
import { fileStorage } from '../lib/file-storage';
import pool from '../lib/db';

interface MigrationOptions {
  dryRun: boolean;
  download: boolean;
  updateDb: boolean;
  cleanup: boolean;
}

class FileMigrator {
  private options: MigrationOptions;

  constructor(options: MigrationOptions) {
    this.options = options;
  }

  async migrate() {
    console.log('🚀 Starting file migration...');
    console.log('Options:', this.options);

    try {
      if (this.options.download) {
        await this.downloadExternalFiles();
      }

      if (this.options.updateDb) {
        await this.updateDatabaseUrls();
      }

      if (this.options.cleanup) {
        await this.cleanupOrphanedFiles();
      }

      console.log('✅ Migration completed successfully!');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    }
  }

  private async downloadExternalFiles() {
    console.log('\n📥 Downloading external files...');

    // Get all external file URLs from database
    const externalUrls = await this.getExternalFileUrls();
    console.log(`Found ${externalUrls.length} external files to download`);

    for (const { table, column, id, url } of externalUrls) {
      try {
        if (this.options.dryRun) {
          console.log(`[DRY RUN] Would download: ${url}`);
          continue;
        }

        console.log(`Downloading: ${url}`);
        
        // Download file
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        // Create File object from response
        const buffer = await response.arrayBuffer();
        const filename = this.extractFilename(url);
        const mimeType = response.headers.get('content-type') || 'application/octet-stream';
        
        const file = new File([buffer], filename, { type: mimeType });
        
        // Determine folder based on table
        const folder = this.getFolderForTable(table);
        
        // Upload to local storage
        const result = await fileStorage.uploadFile(file, folder);
        
        // Update database with new URL
        await pool.query(
          `UPDATE ${table} SET ${column} = ? WHERE ${this.getIdColumn(table)} = ?`,
          [result.url, id]
        );

        console.log(`✅ Migrated: ${filename} -> ${result.url}`);
      } catch (error) {
        console.error(`❌ Failed to migrate ${url}:`, error);
      }
    }
  }

  private async updateDatabaseUrls() {
    console.log('\n🔄 Updating database URLs...');

    const result = await fileManager.migrateFileUrls();
    console.log(`✅ Migrated ${result.migrated} URLs`);
    
    if (result.errors.length > 0) {
      console.log('❌ Errors during migration:');
      result.errors.forEach(error => console.log(`  - ${error}`));
    }
  }

  private async cleanupOrphanedFiles() {
    console.log('\n🧹 Cleaning up orphaned files...');

    if (this.options.dryRun) {
      const orphaned = await fileManager.findOrphanedFiles();
      console.log(`[DRY RUN] Would delete ${orphaned.length} orphaned files`);
      orphaned.forEach(file => console.log(`  - ${file.filename} (${file.folder})`));
      return;
    }

    const result = await fileManager.cleanupOrphanedFiles(7);
    console.log(`✅ Deleted ${result.deletedFiles.length} orphaned files`);
    console.log(`💾 Freed up ${(result.totalSize / 1024 / 1024).toFixed(2)} MB`);
    
    if (result.errors.length > 0) {
      console.log('❌ Cleanup errors:');
      result.errors.forEach(error => console.log(`  - ${error}`));
    }
  }

  private async getExternalFileUrls(): Promise<Array<{
    table: string;
    column: string;
    id: number;
    url: string;
  }>> {
    const urls: Array<{ table: string; column: string; id: number; url: string }> = [];

    // Check courses table
    const [courses] = await pool.query(
      'SELECT course_id, image_url FROM courses WHERE image_url IS NOT NULL AND (image_url LIKE "%imagekit%" OR image_url LIKE "%cloudinary%" OR image_url LIKE "%amazonaws%")'
    );
    (courses as any[]).forEach(row => {
      urls.push({
        table: 'courses',
        column: 'image_url',
        id: row.course_id,
        url: row.image_url
      });
    });

    // Check downloadable_files table
    const [files] = await pool.query(
      'SELECT file_id, file_url FROM downloadable_files WHERE file_url IS NOT NULL AND (file_url LIKE "%imagekit%" OR file_url LIKE "%cloudinary%" OR file_url LIKE "%amazonaws%")'
    );
    (files as any[]).forEach(row => {
      urls.push({
        table: 'downloadable_files',
        column: 'file_url',
        id: row.file_id,
        url: row.file_url
      });
    });

    // Add other tables as needed

    return urls;
  }

  private extractFilename(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      let filename = pathname.substring(pathname.lastIndexOf('/') + 1);
      
      // Remove query parameters
      const queryIndex = filename.indexOf('?');
      if (queryIndex !== -1) {
        filename = filename.substring(0, queryIndex);
      }
      
      return filename || `file_${Date.now()}`;
    } catch {
      return `file_${Date.now()}`;
    }
  }

  private getFolderForTable(table: string): string {
    const folderMap: Record<string, string> = {
      courses: 'courses',
      downloadable_files: 'downloadable_files',
      graduates: 'graduates',
    };
    return folderMap[table] || 'migrated';
  }

  private getIdColumn(table: string): string {
    const idMap: Record<string, string> = {
      courses: 'course_id',
      downloadable_files: 'file_id',
      graduates: 'graduate_id',
    };
    return idMap[table] || 'id';
  }
}

// Parse command line arguments
function parseArgs(): MigrationOptions {
  const args = process.argv.slice(2);
  return {
    dryRun: args.includes('--dry-run'),
    download: args.includes('--download'),
    updateDb: args.includes('--update-db'),
    cleanup: args.includes('--cleanup'),
  };
}

// Main execution
async function main() {
  const options = parseArgs();
  
  // Default to all operations if none specified
  if (!options.download && !options.updateDb && !options.cleanup) {
    options.download = true;
    options.updateDb = true;
    options.cleanup = true;
  }

  const migrator = new FileMigrator(options);
  await migrator.migrate();
  
  // Close database connection
  await pool.end();
}

if (require.main === module) {
  main().catch(console.error);
}
