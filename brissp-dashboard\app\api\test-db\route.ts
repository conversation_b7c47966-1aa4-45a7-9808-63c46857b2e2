/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function GET() {
  try {
    // Test database connection and check for admin users
    const [adminUsers] = await pool.query(
      'SELECT admin_id, email, full_name, is_super_admin FROM admin_users'
    );
    
    // Check if admin_users table exists and what's in it
    const [tables] = await pool.query(
      "SHOW TABLES LIKE 'admin_users'"
    );
    
    return NextResponse.json({ 
      message: 'Database connection successful',
      tablesFound: tables,
      adminUsers: adminUsers,
      adminCount: (adminUsers as any[]).length
    });
  } catch (error) {
    console.error('Database test error:', error);
    return NextResponse.json(
      { 
        error: 'Database connection failed: ' + (error as Error).message,
        details: error
      },
      { status: 500 }
    );
  }
}
