/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/db';
import { RowDataPacket, ResultSetHeader } from 'mysql2';

export interface FYPApplication extends RowDataPacket {
  application_id: number;
  student_name: string;
  email: string;
  phone: string;
  university: string;
  course_of_study: string;
  year_of_study: string;
  student_id?: string;
  supervisor_name?: string;
  supervisor_email?: string;
  project_title?: string;
  project_description?: string;
  project_type: 'research' | 'development' | 'analysis' | 'design' | 'other';
  research_area?: string;
  methodology?: string;
  expected_outcomes?: string;
  timeline_weeks?: number;
  required_resources?: string;
  technical_requirements?: string;
  preferred_supervisor_expertise?: string;
  project_deadline?: Date;
  defense_date?: Date;
  university_requirements?: string;
  additional_notes?: string;
  status: 'pending' | 'reviewed' | 'accepted' | 'rejected' | 'in-progress' | 'completed';
  application_date: Date;
  reviewed_by?: number;
  review_date?: Date;
  review_notes?: string;
  assigned_supervisor?: string;
  project_start_date?: Date;
  project_completion_date?: Date;
  final_report_url?: string;
  presentation_url?: string;
  grade_achieved?: string;
  created_at: Date;
  updated_at: Date;
}

// GET all final year project applications
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const project_type = searchParams.get('type');
    const university = searchParams.get('university');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    let query = `
      SELECT * FROM final_year_project_applications
    `;
    const params: any[] = [];
    const conditions: string[] = [];

    if (status) {
      conditions.push('status = ?');
      params.push(status);
    }

    if (project_type) {
      conditions.push('project_type = ?');
      params.push(project_type);
    }

    if (university) {
      conditions.push('university LIKE ?');
      params.push(`%${university}%`);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY application_date DESC LIMIT ? OFFSET ?';
    params.push(limit, offset);

    const [rows] = await pool.query<FYPApplication[]>(query, params);

    // Get total count for pagination
    let countQuery = 'SELECT COUNT(*) as total FROM final_year_project_applications';
    const countParams: any[] = [];
    
    if (conditions.length > 0) {
      countQuery += ' WHERE ' + conditions.join(' AND ');
      if (status) countParams.push(status);
      if (project_type) countParams.push(project_type);
      if (university) countParams.push(`%${university}%`);
    }

    const [countResult] = await pool.query<RowDataPacket[]>(countQuery, countParams);
    const total = countResult[0].total;

    return NextResponse.json({
      applications: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching FYP applications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch applications' },
      { status: 500 }
    );
  }
}

// POST create new final year project application
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    const {
      student_name, email, phone, university, course_of_study, year_of_study,
      student_id, supervisor_name, supervisor_email, project_title,
      project_description, project_type, research_area, methodology,
      expected_outcomes, timeline_weeks, required_resources, technical_requirements,
      preferred_supervisor_expertise, project_deadline, defense_date,
      university_requirements, additional_notes
    } = data;

    // Validate required fields
    if (!student_name || !email || !phone || !university || !course_of_study || !year_of_study || !project_type) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const [result] = await pool.query<ResultSetHeader>(
      `INSERT INTO final_year_project_applications (
        student_name, email, phone, university, course_of_study, year_of_study,
        student_id, supervisor_name, supervisor_email, project_title,
        project_description, project_type, research_area, methodology,
        expected_outcomes, timeline_weeks, required_resources, technical_requirements,
        preferred_supervisor_expertise, project_deadline, defense_date,
        university_requirements, additional_notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        student_name, email, phone, university, course_of_study, year_of_study,
        student_id, supervisor_name, supervisor_email, project_title,
        project_description, project_type, research_area, methodology,
        expected_outcomes, timeline_weeks, required_resources, technical_requirements,
        preferred_supervisor_expertise, project_deadline, defense_date,
        university_requirements, additional_notes
      ]
    );

    return NextResponse.json(
      { 
        message: 'Application submitted successfully',
        application_id: result.insertId
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating FYP application:', error);
    return NextResponse.json(
      { error: 'Failed to submit application' },
      { status: 500 }
    );
  }
}

// PUT update application status
export async function PUT(request: NextRequest) {
  try {
    const data = await request.json();
    const { application_id, status, review_notes, assigned_supervisor } = data;

    if (!application_id || !status) {
      return NextResponse.json(
        { error: 'Application ID and status are required' },
        { status: 400 }
      );
    }

    const [result] = await pool.query<ResultSetHeader>(
      `UPDATE final_year_project_applications 
       SET status = ?, review_notes = ?, assigned_supervisor = ?, review_date = NOW()
       WHERE application_id = ?`,
      [status, review_notes, assigned_supervisor, application_id]
    );

    if (result.affectedRows === 0) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Application updated successfully' });
  } catch (error) {
    console.error('Error updating FYP application:', error);
    return NextResponse.json(
      { error: 'Failed to update application' },
      { status: 500 }
    );
  }
}
