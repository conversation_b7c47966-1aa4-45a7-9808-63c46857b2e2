/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-no-undef */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
"use client"

import { useState, useEffect } from "react"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent,  CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

import Link from "next/link"
import { PitchDeckApplication } from "@/app/api/pitch-deck-applications/route"
import { ArrowLeft, Building, Calendar, Save, Target, Users } from "lucide-react"

export default function PitchDeckApplicationDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [application, setApplication] = useState<PitchDeckApplication | null>(null)
  const [progress, setProgress] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [formData, setFormData] = useState({
    status: '',
    review_notes: '',
    assigned_consultant: '',
    project_start_date: '',
    project_completion_date: '',
    final_pitch_deck_url: '',
    success_metrics: ''
  })

  useEffect(() => {
    if (params?.id) {
      fetchApplication()
    }
  }, [params?.id])

  const fetchApplication = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/pitch-deck-applications/${params?.id}`)
      const data = await response.json()
      
      setApplication(data.application)
      setProgress(data.progress || [])
      
      // Initialize form data
      setFormData({
        status: data.application.status || '',
        review_notes: data.application.review_notes || '',
        assigned_consultant: data.application.assigned_consultant || '',
        project_start_date: data.application.project_start_date ? new Date(data.application.project_start_date).toISOString().split('T')[0] : '',
        project_completion_date: data.application.project_completion_date ? new Date(data.application.project_completion_date).toISOString().split('T')[0] : '',
        final_pitch_deck_url: data.application.final_pitch_deck_url || '',
        success_metrics: data.application.success_metrics || ''
      })
    } catch (error) {
      console.error('Error fetching application:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleUpdate = async () => {
    try {
      setUpdating(true)
      const response = await fetch(`/api/pitch-deck-applications/${params?.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        fetchApplication()
        alert('Application updated successfully!')
      } else {
        alert('Failed to update application')
      }
    } catch (error) {
      console.error('Error updating application:', error)
      alert('Error updating application')
    } finally {
      setUpdating(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: "secondary" as const, label: "Pending" },
      reviewed: { variant: "outline" as const, label: "Reviewed" },
      accepted: { variant: "default" as const, label: "Accepted" },
      rejected: { variant: "destructive" as const, label: "Rejected" },
      "in-progress": { variant: "default" as const, label: "In Progress" },
      completed: { variant: "default" as const, label: "Completed" }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  if (loading) {
    return <div className="p-6">Loading application details...</div>
  }

  if (!application) {
    return <div className="p-6">Application not found</div>
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/pitch-deck-applications">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Applications
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">{application.applicant_name}</h1>
            <p className="text-gray-600">Pitch Deck Application #{application.application_id}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {getStatusBadge(application.status)}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Application Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Applicant Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Name</Label>
                  <p className="font-medium">{application.applicant_name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Email</Label>
                  <p>{application.email}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Phone</Label>
                  <p>{application.phone}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Company</Label>
                  <p>{application.company_name || 'N/A'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Business Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building className="w-5 h-5 mr-2" />
                Business Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Industry</Label>
                  <p>{application.industry || 'N/A'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Funding Stage</Label>
                  <p className="capitalize">{application.funding_stage.replace('-', ' ')}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Funding Amount</Label>
                  <p>{application.funding_amount || 'N/A'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Team Size</Label>
                  <p>{application.team_size || 'N/A'}</p>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">Business Description</Label>
                <p className="mt-1 text-sm">{application.business_description}</p>
              </div>
              {application.target_audience && (
                <div>
                  <Label className="text-sm font-medium text-gray-500">Target Audience</Label>
                  <p className="mt-1 text-sm">{application.target_audience}</p>
                </div>
              )}
              {application.current_traction && (
                <div>
                  <Label className="text-sm font-medium text-gray-500">Current Traction</Label>
                  <p className="mt-1 text-sm">{application.current_traction}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Project Requirements */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Target className="w-5 h-5 mr-2" />
                Project Requirements
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Budget Range</Label>
                  <p className="capitalize">{application.budget_range}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Preferred Start Date</Label>
                  <p>{application.preferred_start_date ? new Date(application.preferred_start_date).toLocaleDateString() : 'N/A'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Pitch Deadline</Label>
                  <p>{application.pitch_deadline ? new Date(application.pitch_deadline).toLocaleDateString() : 'N/A'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Previous Funding</Label>
                  <p>{application.previous_funding ? 'Yes' : 'No'}</p>
                </div>
              </div>
              {application.specific_requirements && (
                <div>
                  <Label className="text-sm font-medium text-gray-500">Specific Requirements</Label>
                  <p className="mt-1 text-sm">{application.specific_requirements}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Management Panel */}
        <div className="space-y-6">
          {/* Status Management */}
          <Card>
            <CardHeader>
              <CardTitle>Application Management</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="reviewed">Reviewed</SelectItem>
                    <SelectItem value="accepted">Accepted</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                    <SelectItem value="in-progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="assigned_consultant">Assigned Consultant</Label>
                <Input
                  id="assigned_consultant"
                  value={formData.assigned_consultant}
                  onChange={(e) => setFormData(prev => ({ ...prev, assigned_consultant: e.target.value }))}
                  placeholder="Enter consultant name"
                />
              </div>

              <div>
                <Label htmlFor="review_notes">Review Notes</Label>
                <Textarea
                  id="review_notes"
                  value={formData.review_notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, review_notes: e.target.value }))}
                  placeholder="Add review notes..."
                  rows={4}
                />
              </div>

              <Button onClick={handleUpdate} disabled={updating} className="w-full">
                <Save className="w-4 h-4 mr-2" />
                {updating ? 'Updating...' : 'Update Application'}
              </Button>
            </CardContent>
          </Card>

          {/* Project Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Project Timeline
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="project_start_date">Start Date</Label>
                <Input
                  id="project_start_date"
                  type="date"
                  value={formData.project_start_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, project_start_date: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="project_completion_date">Completion Date</Label>
                <Input
                  id="project_completion_date"
                  type="date"
                  value={formData.project_completion_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, project_completion_date: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="final_pitch_deck_url">Final Pitch Deck URL</Label>
                <Input
                  id="final_pitch_deck_url"
                  value={formData.final_pitch_deck_url}
                  onChange={(e) => setFormData(prev => ({ ...prev, final_pitch_deck_url: e.target.value }))}
                  placeholder="https://..."
                />
              </div>

              <div>
                <Label htmlFor="success_metrics">Success Metrics</Label>
                <Textarea
                  id="success_metrics"
                  value={formData.success_metrics}
                  onChange={(e) => setFormData(prev => ({ ...prev, success_metrics: e.target.value }))}
                  placeholder="Document success metrics..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Application Info */}
          <Card>
            <CardHeader>
              <CardTitle>Application Info</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">Applied:</span>
                <span>{new Date(application.application_date).toLocaleDateString()}</span>
              </div>
              {application.review_date && (
                <div className="flex justify-between">
                  <span className="text-gray-500">Reviewed:</span>
                  <span>{new Date(application.review_date).toLocaleDateString()}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-500">Last Updated:</span>
                <span>{new Date(application.updated_at).toLocaleDateString()}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
