"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Eye, Trash2, Cpu, Clock, CheckCircle, XCircle, FileText } from "lucide-react";
import Link from "next/link";

interface InnovationLabApplication {
  application_id: number;
  applicant_name: string;
  email: string;
  phone: string;
  university_organization: string;
  project_title: string;
  innovation_type: string;
  development_stage: string;
  status: 'pending' | 'reviewed' | 'accepted' | 'rejected' | 'in-progress' | 'completed';
  application_date: string;
}
 
export default function InnovationLabApplicationsPage() {
  const [applications, setApplications] = useState<InnovationLabApplication[]>([]);
  const [filteredApplications, setFilteredApplications] = useState<InnovationLabApplication[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'reviewed' | 'accepted' | 'in-progress' | 'completed'>('all');

  useEffect(() => {
    fetchApplications();
  }, []);

  useEffect(() => {
    const filterApplications = () => {
      switch (activeTab) {
        case 'pending':
          setFilteredApplications(applications.filter(app => app.status === 'pending'));
          break;
        case 'reviewed':
          setFilteredApplications(applications.filter(app => app.status === 'reviewed'));
          break;
        case 'accepted':
          setFilteredApplications(applications.filter(app => app.status === 'accepted'));
          break;
        case 'in-progress':
          setFilteredApplications(applications.filter(app => app.status === 'in-progress'));
          break;
        case 'completed':
          setFilteredApplications(applications.filter(app => app.status === 'completed'));
          break;
        default:
          setFilteredApplications(applications);
      }
    };
    
    filterApplications();
  }, [applications, activeTab]);

  const fetchApplications = async () => {
    try {
      const response = await fetch("/api/innovation-lab-applications");
      const data = await response.json();
      setApplications(data.applications || []);
    } catch (error) {
      console.error("Error fetching innovation lab applications:", error);
    }
  };

  const handleDelete = async (applicationId: number) => {
    if (confirm("Are you sure you want to delete this application?")) {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/innovation-lab-applications/${applicationId}`, {
          method: "DELETE",
        });
        if (response.ok) {
          fetchApplications();
        }
      } catch (error) {
        console.error("Error deleting innovation lab application:", error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: "secondary" as const, label: "Pending", className: "bg-yellow-100 text-yellow-800" },
      reviewed: { variant: "outline" as const, label: "Reviewed", className: "bg-blue-100 text-blue-800" },
      accepted: { variant: "default" as const, label: "Accepted", className: "bg-green-100 text-green-800" },
      rejected: { variant: "destructive" as const, label: "Rejected", className: "bg-red-100 text-red-800" },
      "in-progress": { variant: "default" as const, label: "In Progress", className: "bg-purple-100 text-purple-800" },
      completed: { variant: "default" as const, label: "Completed", className: "bg-emerald-100 text-emerald-800" },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <Badge className={config.className}>
        {config.label}
      </Badge>
    );
  };

  const getInnovationTypeBadge = (type: string) => {
    const typeColors = {
      "software": "bg-blue-100 text-blue-800",
      "hardware": "bg-orange-100 text-orange-800",
      "ai-ml": "bg-purple-100 text-purple-800",
      "biotech": "bg-green-100 text-green-800",
      "fintech": "bg-yellow-100 text-yellow-800",
      "edtech": "bg-indigo-100 text-indigo-800",
      "cleantech": "bg-emerald-100 text-emerald-800",
      "other": "bg-gray-100 text-gray-800"
    };
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${typeColors[type as keyof typeof typeColors] || 'bg-gray-100 text-gray-800'}`}>
        {type ? type.charAt(0).toUpperCase() + type.slice(1).replace('-', '/') : 'N/A'}
      </span>
    );
  };

  // Calculate counts for tabs
  const getApplicationCounts = () => {
    return {
      all: applications.length,
      pending: applications.filter(app => app.status === 'pending').length,
      reviewed: applications.filter(app => app.status === 'reviewed').length,
      accepted: applications.filter(app => app.status === 'accepted').length,
      inProgress: applications.filter(app => app.status === 'in-progress').length,
      completed: applications.filter(app => app.status === 'completed').length,
    };
  };

  const counts = getApplicationCounts();

  const ApplicationTable = ({ applications }: { applications: InnovationLabApplication[] }) => (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Applicant</TableHead>
          <TableHead>Project Title</TableHead>
          <TableHead>Innovation Type</TableHead>
          <TableHead>Stage</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Applied Date</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {applications.map((app) => (
          <TableRow key={app.application_id}>
            <TableCell>
              <div>
                <div className="font-medium">{app.applicant_name}</div>
                <div className="text-sm text-gray-500">{app.email}</div>
              </div>
            </TableCell>
            <TableCell>
              <div>
                <div className="font-medium max-w-xs truncate" title={app.project_title}>
                  {app.project_title}
                </div>
                <div className="text-sm text-gray-500">{app.university_organization}</div>
              </div>
            </TableCell>
            <TableCell>{getInnovationTypeBadge(app.innovation_type)}</TableCell>
            <TableCell>
              <span className="capitalize text-sm">
                {app.development_stage.replace('-', ' ')}
              </span>
            </TableCell>
            <TableCell>{getStatusBadge(app.status)}</TableCell>
            <TableCell>{new Date(app.application_date).toLocaleDateString()}</TableCell>
            <TableCell>
              <div className="flex space-x-2">
                <Link href={`/innovation-lab-applications/${app.application_id}`}>
                  <Button variant="outline" size="sm">
                    <Eye className="w-4 h-4" />
                  </Button>
                </Link>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDelete(app.application_id)}
                  disabled={isLoading}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Innovation Lab Applications</h1>
          <p className="text-gray-600">Manage innovation lab applications and track project progress</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
        <div className="bg-white p-6 rounded-lg border shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Applications</p>
              <p className="text-2xl font-bold">{counts.all}</p>
            </div>
            <FileText className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg border shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold">{counts.pending}</p>
            </div>
            <Clock className="h-8 w-8 text-yellow-600" />
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg border shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Reviewed</p>
              <p className="text-2xl font-bold">{counts.reviewed}</p>
            </div>
            <Eye className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg border shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Accepted</p>
              <p className="text-2xl font-bold">{counts.accepted}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg border shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">In Progress</p>
              <p className="text-2xl font-bold">{counts.inProgress}</p>
            </div>
            <Cpu className="h-8 w-8 text-purple-600" />
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg border shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold">{counts.completed}</p>
            </div>
            <XCircle className="h-8 w-8 text-emerald-600" />
          </div>
        </div>
      </div>

      {/* Tabbed Interface */}
      <div className="bg-white rounded-lg border shadow-sm">
        <div className="border-b">
          <nav className="flex space-x-8 px-6">
            <button
              type="button"
              onClick={() => setActiveTab('all')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'all'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              All Applications ({counts.all})
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('pending')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'pending'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Pending ({counts.pending})
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('reviewed')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'reviewed'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Reviewed ({counts.reviewed})
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('accepted')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'accepted'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Accepted ({counts.accepted})
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('in-progress')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'in-progress'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              In Progress ({counts.inProgress})
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('completed')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'completed'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Completed ({counts.completed})
            </button>
          </nav>
        </div>

        <div className="p-6">
          {isLoading ? (
            <div className="text-center py-8">Loading applications...</div>
          ) : filteredApplications.length === 0 ? (
            <div className="text-center py-8">
              <p>No applications found for this status.</p>
            </div>
          ) : (
            <ApplicationTable applications={filteredApplications} />
          )}
        </div>
      </div>
    </div>
  );
}
