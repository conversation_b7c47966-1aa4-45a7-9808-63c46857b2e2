{"version": 3, "sources": [], "sections": [{"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/lib/file-storage.ts"], "sourcesContent": ["import { writeFile, mkdir, unlink, access } from 'fs/promises';\nimport { join } from 'path';\nimport { v4 as uuidv4 } from 'uuid';\n\nexport interface FileUploadResult {\n  url: string;\n  filename: string;\n  originalName: string;\n  size: number;\n  mimeType: string;\n  path: string;\n}\n\nexport interface FileValidationOptions {\n  maxSize?: number; // in bytes\n  allowedTypes?: string[];\n  allowedExtensions?: string[];\n}\n\nexport class LocalFileStorage {\n  private baseDir: string;\n  private baseUrl: string;\n\n  constructor(baseDir: string = 'public/uploads', baseUrl: string = '/uploads') {\n    this.baseDir = baseDir;\n    this.baseUrl = baseUrl;\n  }\n\n  /**\n   * Validate file before upload\n   */\n  validateFile(file: File, options: FileValidationOptions = {}): { valid: boolean; error?: string } {\n    const {\n      maxSize = 10 * 1024 * 1024, // 10MB default\n      allowedTypes = [\n        'image/jpeg', 'image/png', 'image/gif', 'image/webp',\n        'application/pdf', 'application/msword',\n        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n        'application/vnd.ms-excel',\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n        'text/plain', 'text/csv'\n      ],\n      allowedExtensions = [\n        '.jpg', '.jpeg', '.png', '.gif', '.webp',\n        '.pdf', '.doc', '.docx', '.xls', '.xlsx',\n        '.txt', '.csv'\n      ]\n    } = options;\n\n    // Check file size\n    if (file.size > maxSize) {\n      return {\n        valid: false,\n        error: `File size exceeds maximum allowed size of ${Math.round(maxSize / (1024 * 1024))}MB`\n      };\n    }\n\n    // Check MIME type\n    if (!allowedTypes.includes(file.type)) {\n      return {\n        valid: false,\n        error: `File type ${file.type} is not allowed`\n      };\n    }\n\n    // Check file extension\n    const extension = this.getFileExtension(file.name);\n    if (!allowedExtensions.includes(extension.toLowerCase())) {\n      return {\n        valid: false,\n        error: `File extension ${extension} is not allowed`\n      };\n    }\n\n    return { valid: true };\n  }\n\n  /**\n   * Upload file to local storage\n   */\n  async uploadFile(file: File, folder: string = 'general'): Promise<FileUploadResult> {\n    // Validate file\n    const validation = this.validateFile(file);\n    if (!validation.valid) {\n      throw new Error(validation.error);\n    }\n\n    // Generate unique filename\n    const extension = this.getFileExtension(file.name);\n    const filename = `${uuidv4()}${extension}`;\n    \n    // Create folder path\n    const folderPath = join(this.baseDir, folder);\n    const filePath = join(folderPath, filename);\n    \n    try {\n      // Ensure directory exists\n      await mkdir(folderPath, { recursive: true });\n      \n      // Convert file to buffer and write\n      const bytes = await file.arrayBuffer();\n      const buffer = Buffer.from(bytes);\n      await writeFile(filePath, buffer);\n      \n      // Return file info\n      return {\n        url: `${this.baseUrl}/${folder}/${filename}`,\n        filename,\n        originalName: file.name,\n        size: file.size,\n        mimeType: file.type,\n        path: filePath\n      };\n    } catch (error) {\n      console.error('Error uploading file:', error);\n      throw new Error('Failed to upload file');\n    }\n  }\n\n  /**\n   * Delete file from local storage\n   */\n  async deleteFile(filePath: string): Promise<boolean> {\n    try {\n      await unlink(filePath);\n      return true;\n    } catch (error) {\n      console.error('Error deleting file:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Check if file exists\n   */\n  async fileExists(filePath: string): Promise<boolean> {\n    try {\n      await access(filePath);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * Get file extension from filename\n   */\n  private getFileExtension(filename: string): string {\n    return filename.substring(filename.lastIndexOf('.'));\n  }\n\n  /**\n   * Generate file URL from path\n   */\n  getFileUrl(folder: string, filename: string): string {\n    return `${this.baseUrl}/${folder}/${filename}`;\n  }\n\n  /**\n   * Extract folder and filename from URL\n   */\n  parseFileUrl(url: string): { folder: string; filename: string } | null {\n    const urlParts = url.replace(this.baseUrl + '/', '').split('/');\n    if (urlParts.length >= 2) {\n      const filename = urlParts.pop()!;\n      const folder = urlParts.join('/');\n      return { folder, filename };\n    }\n    return null;\n  }\n\n  /**\n   * Get full file path from URL\n   */\n  getFilePathFromUrl(url: string): string | null {\n    const parsed = this.parseFileUrl(url);\n    if (parsed) {\n      return join(this.baseDir, parsed.folder, parsed.filename);\n    }\n    return null;\n  }\n}\n\n// Default instance\nexport const fileStorage = new LocalFileStorage();\n\n// File type categories for easier validation\nexport const FILE_CATEGORIES = {\n  images: {\n    types: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\n    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp']\n  },\n  documents: {\n    types: [\n      'application/pdf',\n      'application/msword',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n      'application/vnd.ms-excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n      'text/plain',\n      'text/csv'\n    ],\n    extensions: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt', '.csv']\n  },\n  all: {\n    types: [\n      'image/jpeg', 'image/png', 'image/gif', 'image/webp',\n      'application/pdf', 'application/msword',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n      'application/vnd.ms-excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n      'text/plain', 'text/csv'\n    ],\n    extensions: [\n      '.jpg', '.jpeg', '.png', '.gif', '.webp',\n      '.pdf', '.doc', '.docx', '.xls', '.xlsx',\n      '.txt', '.csv'\n    ]\n  }\n};\n\n// Helper function to get validation options by category\nexport function getValidationOptions(category: keyof typeof FILE_CATEGORIES, maxSize?: number) {\n  return {\n    allowedTypes: FILE_CATEGORIES[category].types,\n    allowedExtensions: FILE_CATEGORIES[category].extensions,\n    maxSize\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAiBO,MAAM;IACH,QAAgB;IAChB,QAAgB;IAExB,YAAY,UAAkB,gBAAgB,EAAE,UAAkB,UAAU,CAAE;QAC5E,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA;;GAEC,GACD,aAAa,IAAU,EAAE,UAAiC,CAAC,CAAC,EAAsC;QAChG,MAAM,EACJ,UAAU,KAAK,OAAO,IAAI,EAC1B,eAAe;YACb;YAAc;YAAa;YAAa;YACxC;YAAmB;YACnB;YACA;YACA;YACA;YAAc;SACf,EACD,oBAAoB;YAClB;YAAQ;YAAS;YAAQ;YAAQ;YACjC;YAAQ;YAAQ;YAAS;YAAQ;YACjC;YAAQ;SACT,EACF,GAAG;QAEJ,kBAAkB;QAClB,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,OAAO;gBACL,OAAO;gBACP,OAAO,CAAC,0CAA0C,EAAE,KAAK,KAAK,CAAC,UAAU,CAAC,OAAO,IAAI,GAAG,EAAE,CAAC;YAC7F;QACF;QAEA,kBAAkB;QAClB,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrC,OAAO;gBACL,OAAO;gBACP,OAAO,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,eAAe,CAAC;YAChD;QACF;QAEA,uBAAuB;QACvB,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI;QACjD,IAAI,CAAC,kBAAkB,QAAQ,CAAC,UAAU,WAAW,KAAK;YACxD,OAAO;gBACL,OAAO;gBACP,OAAO,CAAC,eAAe,EAAE,UAAU,eAAe,CAAC;YACrD;QACF;QAEA,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA;;GAEC,GACD,MAAM,WAAW,IAAU,EAAE,SAAiB,SAAS,EAA6B;QAClF,gBAAgB;QAChB,MAAM,aAAa,IAAI,CAAC,YAAY,CAAC;QACrC,IAAI,CAAC,WAAW,KAAK,EAAE;YACrB,MAAM,IAAI,MAAM,WAAW,KAAK;QAClC;QAEA,2BAA2B;QAC3B,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI;QACjD,MAAM,WAAW,GAAG,CAAA,GAAA,oLAAA,CAAA,KAAM,AAAD,MAAM,WAAW;QAE1C,qBAAqB;QACrB,MAAM,aAAa,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,OAAO,EAAE;QACtC,MAAM,WAAW,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,YAAY;QAElC,IAAI;YACF,0BAA0B;YAC1B,MAAM,CAAA,GAAA,qHAAA,CAAA,QAAK,AAAD,EAAE,YAAY;gBAAE,WAAW;YAAK;YAE1C,mCAAmC;YACnC,MAAM,QAAQ,MAAM,KAAK,WAAW;YACpC,MAAM,SAAS,OAAO,IAAI,CAAC;YAC3B,MAAM,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE,UAAU;YAE1B,mBAAmB;YACnB,OAAO;gBACL,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,UAAU;gBAC5C;gBACA,cAAc,KAAK,IAAI;gBACvB,MAAM,KAAK,IAAI;gBACf,UAAU,KAAK,IAAI;gBACnB,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,WAAW,QAAgB,EAAoB;QACnD,IAAI;YACF,MAAM,CAAA,GAAA,qHAAA,CAAA,SAAM,AAAD,EAAE;YACb,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,WAAW,QAAgB,EAAoB;QACnD,IAAI;YACF,MAAM,CAAA,GAAA,qHAAA,CAAA,SAAM,AAAD,EAAE;YACb,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA;;GAEC,GACD,AAAQ,iBAAiB,QAAgB,EAAU;QACjD,OAAO,SAAS,SAAS,CAAC,SAAS,WAAW,CAAC;IACjD;IAEA;;GAEC,GACD,WAAW,MAAc,EAAE,QAAgB,EAAU;QACnD,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,UAAU;IAChD;IAEA;;GAEC,GACD,aAAa,GAAW,EAA+C;QACrE,MAAM,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,IAAI,KAAK,CAAC;QAC3D,IAAI,SAAS,MAAM,IAAI,GAAG;YACxB,MAAM,WAAW,SAAS,GAAG;YAC7B,MAAM,SAAS,SAAS,IAAI,CAAC;YAC7B,OAAO;gBAAE;gBAAQ;YAAS;QAC5B;QACA,OAAO;IACT;IAEA;;GAEC,GACD,mBAAmB,GAAW,EAAiB;QAC7C,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC;QACjC,IAAI,QAAQ;YACV,OAAO,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE,OAAO,QAAQ;QAC1D;QACA,OAAO;IACT;AACF;AAGO,MAAM,cAAc,IAAI;AAGxB,MAAM,kBAAkB;IAC7B,QAAQ;QACN,OAAO;YAAC;YAAc;YAAa;YAAa;SAAa;QAC7D,YAAY;YAAC;YAAQ;YAAS;YAAQ;YAAQ;SAAQ;IACxD;IACA,WAAW;QACT,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;YAAC;YAAQ;YAAQ;YAAS;YAAQ;YAAS;YAAQ;SAAO;IACxE;IACA,KAAK;QACH,OAAO;YACL;YAAc;YAAa;YAAa;YACxC;YAAmB;YACnB;YACA;YACA;YACA;YAAc;SACf;QACD,YAAY;YACV;YAAQ;YAAS;YAAQ;YAAQ;YACjC;YAAQ;YAAQ;YAAS;YAAQ;YACjC;YAAQ;SACT;IACH;AACF;AAGO,SAAS,qBAAqB,QAAsC,EAAE,OAAgB;IAC3F,OAAO;QACL,cAAc,eAAe,CAAC,SAAS,CAAC,KAAK;QAC7C,mBAAmB,eAAe,CAAC,SAAS,CAAC,UAAU;QACvD;IACF;AACF"}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/app/api/files/%5B...path%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { readFile, stat } from 'fs/promises';\nimport { join } from 'path';\nimport { fileStorage } from '@/lib/file-storage';\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { path: string[] } }\n) {\n  try {\n    const filePath = params.path.join('/');\n    const fullPath = join(process.cwd(), 'public/uploads', filePath);\n    \n    // Check if file exists\n    const exists = await fileStorage.fileExists(fullPath);\n    if (!exists) {\n      return NextResponse.json(\n        { error: 'File not found' },\n        { status: 404 }\n      );\n    }\n\n    // Get file stats\n    const stats = await stat(fullPath);\n    \n    // Read file\n    const fileBuffer = await readFile(fullPath);\n    \n    // Determine content type based on file extension\n    const extension = filePath.substring(filePath.lastIndexOf('.'));\n    const contentType = getContentType(extension);\n    \n    // Create response with proper headers\n    const response = new NextResponse(fileBuffer);\n    \n    // Set content headers\n    response.headers.set('Content-Type', contentType);\n    response.headers.set('Content-Length', stats.size.toString());\n    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable');\n    \n    // Set CORS headers to allow access from Academy\n    response.headers.set('Access-Control-Allow-Origin', '*');\n    response.headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');\n    response.headers.set('Access-Control-Allow-Headers', 'Content-Type');\n    \n    // Set content disposition for downloads\n    if (request.nextUrl.searchParams.get('download') === 'true') {\n      const filename = filePath.substring(filePath.lastIndexOf('/') + 1);\n      response.headers.set('Content-Disposition', `attachment; filename=\"${filename}\"`);\n    }\n    \n    return response;\n  } catch (error) {\n    console.error('Error serving file:', error);\n    return NextResponse.json(\n      { error: 'Failed to serve file' },\n      { status: 500 }\n    );\n  }\n}\n\n// Handle OPTIONS requests for CORS\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type',\n    },\n  });\n}\n\nfunction getContentType(extension: string): string {\n  const contentTypes: Record<string, string> = {\n    '.jpg': 'image/jpeg',\n    '.jpeg': 'image/jpeg',\n    '.png': 'image/png',\n    '.gif': 'image/gif',\n    '.webp': 'image/webp',\n    '.pdf': 'application/pdf',\n    '.doc': 'application/msword',\n    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n    '.xls': 'application/vnd.ms-excel',\n    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n    '.txt': 'text/plain',\n    '.csv': 'text/csv',\n  };\n  \n  return contentTypes[extension.toLowerCase()] || 'application/octet-stream';\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAkC;IAE1C,IAAI;QACF,MAAM,WAAW,OAAO,IAAI,CAAC,IAAI,CAAC;QAClC,MAAM,WAAW,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI,kBAAkB;QAEvD,uBAAuB;QACvB,MAAM,SAAS,MAAM,wHAAA,CAAA,cAAW,CAAC,UAAU,CAAC;QAC5C,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,iBAAiB;QACjB,MAAM,QAAQ,MAAM,CAAA,GAAA,qHAAA,CAAA,OAAI,AAAD,EAAE;QAEzB,YAAY;QACZ,MAAM,aAAa,MAAM,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,EAAE;QAElC,iDAAiD;QACjD,MAAM,YAAY,SAAS,SAAS,CAAC,SAAS,WAAW,CAAC;QAC1D,MAAM,cAAc,eAAe;QAEnC,sCAAsC;QACtC,MAAM,WAAW,IAAI,gIAAA,CAAA,eAAY,CAAC;QAElC,sBAAsB;QACtB,SAAS,OAAO,CAAC,GAAG,CAAC,gBAAgB;QACrC,SAAS,OAAO,CAAC,GAAG,CAAC,kBAAkB,MAAM,IAAI,CAAC,QAAQ;QAC1D,SAAS,OAAO,CAAC,GAAG,CAAC,iBAAiB;QAEtC,gDAAgD;QAChD,SAAS,OAAO,CAAC,GAAG,CAAC,+BAA+B;QACpD,SAAS,OAAO,CAAC,GAAG,CAAC,gCAAgC;QACrD,SAAS,OAAO,CAAC,GAAG,CAAC,gCAAgC;QAErD,wCAAwC;QACxC,IAAI,QAAQ,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,QAAQ;YAC3D,MAAM,WAAW,SAAS,SAAS,CAAC,SAAS,WAAW,CAAC,OAAO;YAChE,SAAS,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC;QAClF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF;AAEA,SAAS,eAAe,SAAiB;IACvC,MAAM,eAAuC;QAC3C,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,QAAQ;IACV;IAEA,OAAO,YAAY,CAAC,UAAU,WAAW,GAAG,IAAI;AAClD"}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}