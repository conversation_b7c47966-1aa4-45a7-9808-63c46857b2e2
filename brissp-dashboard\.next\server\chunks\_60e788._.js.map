{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/node_modules/next/dist/src/build/templates/app-route.ts"], "sourcesContent": ["import {\n  AppRouteRouteModule,\n  type AppRouteRouteModuleOptions,\n} from '../../server/route-modules/app-route/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\nimport { patchFetch as _patchFetch } from '../../server/lib/patch-fetch'\n\nimport * as userland from 'VAR_USERLAND'\n\n// These are injected by the loader afterwards. This is injected as a variable\n// instead of a replacement because this could also be `undefined` instead of\n// an empty string.\ndeclare const nextConfigOutput: AppRouteRouteModuleOptions['nextConfigOutput']\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\n// INJECT:nextConfigOutput\n\nconst routeModule = new AppRouteRouteModule({\n  definition: {\n    kind: RouteKind.APP_ROUTE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    filename: 'VAR_DEFINITION_FILENAME',\n    bundlePath: 'VAR_DEFINITION_BUNDLE_PATH',\n  },\n  resolvedPagePath: 'VAR_RESOLVED_PAGE_PATH',\n  nextConfigOutput,\n  userland,\n})\n\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule\n\nfunction patchFetch() {\n  return _patchFetch({\n    workAsyncStorage,\n    workUnitAsyncStorage,\n  })\n}\n\nexport {\n  routeModule,\n  workAsyncStorage,\n  workUnitAsyncStorage,\n  serverHooks,\n  patchFetch,\n}\n"], "names": ["AppRouteRouteModule", "RouteKind", "patchFetch", "_patchFetch", "userland", "routeModule", "definition", "kind", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "workAsyncStorage", "workUnitAsyncStorage", "serverHooks"], "mappings": ";;;;;;;AAAA,SACEA,mBAAmB,QAEd,uDAAsD;AAC7D,SAASC,SAAS,QAAQ,0BAAyB;AACnD,SAASC,cAAcC,WAAW,QAAQ,+BAA8B;AAExE,YAAYC,cAAc,eAAc;;;;;AAOxC,2EAA2E;AAC3E,UAAU;AACV,MAAA,mBAAA,CAA0B;AAE1B,MAAMC,cAAc,8MAAIL,sBAAAA,CAAoB;IAC1CM,YAAY;QACVC,MAAMN,6KAAAA,CAAUO,SAAS;QACzBC,MAAM;QACNC,UAAU;QACVC,UAAU;QACVC,YAAY;IACd;IACAC,kBAAkB;IAClBC;cACAV;AACF;AAEA,2EAA2E;AAC3E,2EAA2E;AAC3E,mCAAmC;AACnC,MAAM,EAAEW,gBAAgB,EAAEC,oBAAoB,EAAEC,WAAW,EAAE,GAAGZ;AAEhE,SAASH;IACP,oLAAOC,aAAAA,EAAY;QACjBY;QACAC;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}