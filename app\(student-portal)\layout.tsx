'use client';

import { useState } from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  GraduationCap,
  BookOpen,
  ClipboardList,
  Key,
  Bell,
  Clock,
  LogOut,
  Menu,
  Loader2,
  FileText,
  Lightbulb,
  Briefcase,
  Presentation,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { name: 'My Applications', href: '/applications', icon: FileText },
  { name: 'Apply for Courses', href: '/registration', icon: ClipboardList },
  { name: 'My Courses', href: '/course', icon: BookOpen },
  { name: 'Result', href: '/result', icon: Clock },
  { name: 'Notice', href: '/notice', icon: Bell },
  { name: 'Change Password', href: '/account/change-password', icon: Key },
];

const applicationLinks = [
  { name: 'Final Year Project', href: '/apply/final-year-project', icon: GraduationCap },
  { name: 'Pitch Deck Service', href: '/apply/pitch-deck', icon: Presentation },
  { name: 'Innovation Lab', href: '/apply/innovation-lab', icon: Lightbulb },
  { name: 'Internship Program', href: '/apply/internship', icon: Briefcase },
];

export default function StudentPortalLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleLogout = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
      
      if (response.ok) {
        // Clear any client-side state if needed
        router.push('/');
      } else {
        console.error('Logout failed:', await response.text());
      }
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Sidebar */}
      <div
        className={cn(
          'fixed inset-y-0 left-0 z-50 w-64 bg-amber-600 transition-transform duration-300 ease-in-out lg:translate-x-0',
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        )}
      >
        <div className="flex h-full flex-col">
          {/* Logo */}
          <div className="flex h-16 items-center gap-2 px-6">
            <GraduationCap className="h-8 w-8 text-white" />
            <span className="font-semibold text-xl text-white">Academy</span>
          </div>

          {/* Navigation */}
          <nav className="flex-1 space-y-1 px-3 py-4">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="group flex items-center rounded-lg px-3 py-2 text-sm font-medium text-white hover:bg-white/10"
              >
                <item.icon className="mr-3 h-5 w-5" />
                {item.name}
              </Link>
            ))}

            {/* Application Services Section */}
            <div className="pt-4 mt-4 border-t border-white/20">
              <p className="px-3 text-xs font-semibold text-white/60 uppercase tracking-wider mb-2">
                Apply for Services
              </p>
              {applicationLinks.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="group flex items-center rounded-lg px-3 py-2 text-sm font-medium text-white hover:bg-white/10"
                >
                  <item.icon className="mr-3 h-4 w-4" />
                  {item.name}
                </Link>
              ))}
            </div>
          </nav>

          {/* Logout */}
          <div className="p-4">
            <Button
              variant="ghost"
              className="w-full justify-start text-white hover:bg-white/10"
              onClick={handleLogout}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="mr-3 h-5 w-5 animate-spin" />
              ) : (
                <LogOut className="mr-3 h-5 w-5" />
              )}
              {isLoading ? 'Logging out...' : 'Logout'}
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div
        className={cn(
          'transition-margin duration-300 ease-in-out min-h-screen bg-background',
          sidebarOpen ? 'lg:ml-64' : 'ml-0'
        )}
      >
        {/* Toggle Button */}
        <Button
          variant="ghost"
          size="icon"
          className="fixed top-4 left-4 z-50 lg:hidden"
          onClick={() => setSidebarOpen(!sidebarOpen)}
        >
          <Menu className="h-6 w-6" />
        </Button>

        <main className="py-6 px-4 sm:px-6 lg:px-8">{children}</main>
      </div>
    </div>
  );
}
