/* eslint-disable react-hooks/exhaustive-deps */
"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>H<PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, User, Lightbulb, Cpu, Target, Users, Calendar, Settings, Save, Wrench} from "lucide-react"

interface InnovationLabApplication {
  application_id: number;
  applicant_name: string;
  email: string;
  phone: string;
  university_organization: string;
  project_title: string;
  idea_description: string;
  innovation_type: string;
  development_stage: string;
  problem_statement?: string;
  solution_approach?: string;
  target_market?: string;
  software_needs?: string;
  hardware_needs?: string;
  technologies_involved?: string;
  technical_expertise_required?: string;
  project_duration?: string;
  expected_outcomes?: string;
  success_metrics?: string;
  project_deadline?: string;
  team_size?: number;
  team_member_roles?: string;
  team_experience?: string;
  previous_projects?: string;
  lab_access_needs?: string;
  equipment_requirements?: string;
  funding_requirements?: string;
  mentorship_needs?: string;
  collaboration_interests?: boolean;
  additional_notes?: string;
  status: 'pending' | 'reviewed' | 'accepted' | 'rejected' | 'in-progress' | 'completed';
  application_date: string;
  reviewed_by?: number;
  review_date?: string;
  review_notes?: string;
}

export default function InnovationLabApplicationDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const [application, setApplication] = useState<InnovationLabApplication | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [status, setStatus] = useState("")
  const [reviewNotes, setReviewNotes] = useState("")

  useEffect(() => {
    if (params?.id) {
      fetchApplication()
    }
  }, [params?.id])

  const fetchApplication = async () => {
    try {
      const response = await fetch(`/api/innovation-lab-applications/${params?.id}`)
      const data = await response.json()
      
      if (response.ok) {
        setApplication(data.application)
        setStatus(data.application.status)
        setReviewNotes(data.application.review_notes || "")
      }
    } catch (error) {
      console.error('Error fetching application:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleUpdate = async () => {
    if (!application) return

    setUpdating(true)
    try {
      const response = await fetch(`/api/innovation-lab-applications/${application.application_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status,
          review_notes: reviewNotes,
        }),
      })

      if (response.ok) {
        fetchApplication()
        alert('Application updated successfully!')
      }
    } catch (error) {
      console.error('Error updating application:', error)
      alert('Error updating application')
    } finally {
      setUpdating(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: "secondary" as const, label: "Pending", className: "bg-yellow-100 text-yellow-800" },
      reviewed: { variant: "outline" as const, label: "Reviewed", className: "bg-blue-100 text-blue-800" },
      accepted: { variant: "default" as const, label: "Accepted", className: "bg-green-100 text-green-800" },
      rejected: { variant: "destructive" as const, label: "Rejected", className: "bg-red-100 text-red-800" },
      "in-progress": { variant: "default" as const, label: "In Progress", className: "bg-purple-100 text-purple-800" },
      completed: { variant: "default" as const, label: "Completed", className: "bg-emerald-100 text-emerald-800" },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <Badge className={config.className}>
        {config.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center py-8">Loading application details...</div>
      </div>
    )
  }

  if (!application) {
    return (
      <div className="p-6">
        <div className="text-center py-8">Application not found</div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Innovation Lab Application Details</h1>
            <p className="text-gray-600">Application #{application.application_id}</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          {getStatusBadge(application.status)}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Applicant Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="w-5 h-5" />
                <span>Applicant Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Full Name</label>
                  <p className="text-sm">{application.applicant_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <p className="text-sm">{application.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Phone</label>
                  <p className="text-sm">{application.phone}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">University/Organization</label>
                  <p className="text-sm">{application.university_organization}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Project Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Lightbulb className="w-5 h-5" />
                <span>Project Details</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Project Title</label>
                  <p className="text-sm font-semibold">{application.project_title}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Innovation Type</label>
                  <p className="text-sm capitalize">{application.innovation_type.replace('-', '/')}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Development Stage</label>
                  <p className="text-sm capitalize">{application.development_stage.replace('-', ' ')}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Project Duration</label>
                  <p className="text-sm">{application.project_duration || 'Not specified'}</p>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Project Description</label>
                  <p className="text-sm mt-1 p-3 bg-gray-50 rounded">{application.idea_description}</p>
                </div>
                
                {application.problem_statement && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Problem Statement</label>
                    <p className="text-sm mt-1 p-3 bg-gray-50 rounded">{application.problem_statement}</p>
                  </div>
                )}
                
                {application.solution_approach && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Solution Approach</label>
                    <p className="text-sm mt-1 p-3 bg-gray-50 rounded">{application.solution_approach}</p>
                  </div>
                )}
                
                {application.target_market && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Target Market</label>
                    <p className="text-sm mt-1 p-3 bg-gray-50 rounded">{application.target_market}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Technical Requirements */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Wrench className="w-5 h-5" />
                <span>Technical Requirements</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {application.technologies_involved && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Technologies Involved</label>
                  <p className="text-sm mt-1">{application.technologies_involved}</p>
                </div>
              )}
              {application.software_needs && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Software Requirements</label>
                  <p className="text-sm mt-1">{application.software_needs}</p>
                </div>
              )}
              {application.hardware_needs && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Hardware Requirements</label>
                  <p className="text-sm mt-1">{application.hardware_needs}</p>
                </div>
              )}
              {application.technical_expertise_required && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Technical Expertise Required</label>
                  <p className="text-sm mt-1">{application.technical_expertise_required}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Goals and Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="w-5 h-5" />
                <span>Goals and Timeline</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Project Deadline</label>
                  <p className="text-sm">{application.project_deadline ? new Date(application.project_deadline).toLocaleDateString() : 'Not specified'}</p>
                </div>
              </div>
              
              {application.expected_outcomes && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Expected Outcomes</label>
                  <p className="text-sm mt-1">{application.expected_outcomes}</p>
                </div>
              )}
              {application.success_metrics && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Success Metrics</label>
                  <p className="text-sm mt-1">{application.success_metrics}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Team Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="w-5 h-5" />
                <span>Team Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Team Size</label>
                  <p className="text-sm">{application.team_size || 'Not specified'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Collaboration Interest</label>
                  <p className="text-sm">{application.collaboration_interests ? 'Yes' : 'No'}</p>
                </div>
              </div>

              {application.team_member_roles && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Team Member Roles</label>
                  <p className="text-sm mt-1">{application.team_member_roles}</p>
                </div>
              )}
              {application.team_experience && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Team Experience</label>
                  <p className="text-sm mt-1">{application.team_experience}</p>
                </div>
              )}
              {application.previous_projects && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Previous Projects</label>
                  <p className="text-sm mt-1">{application.previous_projects}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Resource Requirements */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Cpu className="w-5 h-5" />
                <span>Resource Requirements</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {application.lab_access_needs && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Lab Access Needs</label>
                  <p className="text-sm mt-1">{application.lab_access_needs}</p>
                </div>
              )}
              {application.equipment_requirements && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Equipment Requirements</label>
                  <p className="text-sm mt-1">{application.equipment_requirements}</p>
                </div>
              )}
              {application.funding_requirements && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Funding Requirements</label>
                  <p className="text-sm mt-1">{application.funding_requirements}</p>
                </div>
              )}
              {application.mentorship_needs && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Mentorship Needs</label>
                  <p className="text-sm mt-1">{application.mentorship_needs}</p>
                </div>
              )}
              {application.additional_notes && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Additional Notes</label>
                  <p className="text-sm mt-1 p-3 bg-gray-50 rounded">{application.additional_notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Application Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="w-5 h-5" />
                <span>Application Management</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">Status</label>
                <Select value={status} onValueChange={setStatus}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="reviewed">Reviewed</SelectItem>
                    <SelectItem value="accepted">Accepted</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                    <SelectItem value="in-progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Review Notes</label>
                <Textarea
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  placeholder="Add review notes..."
                  className="mt-1"
                  rows={4}
                />
              </div>

              <Button
                onClick={handleUpdate}
                disabled={updating}
                className="w-full"
              >
                <Save className="w-4 h-4 mr-2" />
                {updating ? 'Updating...' : 'Update Application'}
              </Button>
            </CardContent>
          </Card>

          {/* Application Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="w-5 h-5" />
                <span>Application Timeline</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Applied Date</label>
                <p className="text-sm">{new Date(application.application_date).toLocaleDateString()}</p>
              </div>
              {application.review_date && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Review Date</label>
                  <p className="text-sm">{new Date(application.review_date).toLocaleDateString()}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
